/* Custom Calendar Styling for Reservation Page */

/* Main Calendar Container */
.ui-datepicker {
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    padding: 0;
    width: 280px !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fff;
    z-index: 1060 !important;
}

/* Calendar Header */
.ui-datepicker .ui-datepicker-header {
    background: #fff;
    color: #212529;
    border: none;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 8px 8px 0 0;
    padding: 10px;
    position: relative;
}

.ui-datepicker .ui-datepicker-title {
    font-weight: 600;
    text-align: center;
    margin: 0;
    line-height: 1.8;
    font-size: 16px;
}

/* Navigation Arrows */
.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    cursor: pointer;
    text-align: center;
    line-height: 30px;
    color: #212529;
    text-decoration: none;
    background: none;
    border: none;
}

.ui-datepicker .ui-datepicker-prev {
    left: 5px;
}

.ui-datepicker .ui-datepicker-next {
    right: 5px;
}

.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.ui-datepicker .ui-datepicker-prev span i,
.ui-datepicker .ui-datepicker-next span i {
    font-size: 16px;
    color: #212529;
}

/* Calendar Table */
.ui-datepicker table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 14px;
}

/* Day Names (Su, Mo, Tu, etc.) */
.ui-datepicker th {
    text-align: center;
    padding: 8px 0;
    color: #212529;
    font-weight: 600;
    border: none;
    font-size: 13px;
}

/* Calendar Days */
.ui-datepicker td {
    border: none;
    padding: 1px;
    text-align: center;
}

.ui-datepicker td span,
.ui-datepicker td a {
    display: block;
    padding: 8px 0;
    text-align: center;
    text-decoration: none;
    width: 32px;
    height: 32px;
    line-height: 16px;
    margin: 0 auto;
    font-weight: normal;
    color: #212529;
}

/* Hover State */
.ui-datepicker td a.ui-state-default:hover {
    background-color: #f0f0f0;
}

/* Selected Date */
.ui-datepicker td a.ui-state-active {
    background-color: #0d6efd;
    color: white;
    font-weight: 600;
    border-radius: 0;
    box-shadow: none;
    border: 2px solid #0d6efd;
}

/* Today's Date */
.ui-datepicker td a.ui-state-highlight {
    background-color: #e9ecef;
    color: #212529;
}

/* Days from Other Months */
.ui-datepicker td.ui-datepicker-other-month a.ui-state-default {
    color: #aaa;
}

/* Footer with Buttons */
.ui-datepicker .ui-datepicker-buttonpane {
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    margin: 0;
    padding: 5px;
    display: flex;
    justify-content: space-between;
    border-radius: 0 0 8px 8px;
}

.ui-datepicker .ui-datepicker-buttonpane button {
    border: none;
    background: transparent;
    color: #0d6efd;
    font-weight: 500;
    cursor: pointer;
    padding: 5px 10px;
    font-size: 14px;
}

.ui-datepicker .ui-datepicker-buttonpane button:hover {
    text-decoration: underline;
}

/* Custom Footer with Today and Clear Buttons */
.ui-datepicker-footer {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    border-top: 1px solid #e0e0e0;
}

.ui-datepicker-clear,
.ui-datepicker-today {
    color: #0d6efd;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    padding: 5px;
}

.ui-datepicker-clear:hover,
.ui-datepicker-today:hover {
    text-decoration: underline;
}
