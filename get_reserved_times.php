<?php
// Include configuration and functions
include 'config/config.php';
include 'includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if date parameter is provided
if (!isset($_GET['date']) || empty($_GET['date'])) {
    echo json_encode(['error' => 'Date parameter is required']);
    exit;
}

// Get and sanitize date parameter
$date = sanitize($_GET['date']);

// Get and sanitize guests parameter (optional)
$guests = isset($_GET['guests']) ? (int)$_GET['guests'] : 2; // Default to 2 guests if not specified

// Make sure date is in YYYY-MM-DD format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    // Try to convert from MM/DD/YYYY format
    $date_parts = explode('/', $date);
    if (count($date_parts) === 3) {
        $month = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
        $day = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
        $year = $date_parts[2];
        $date = "$year-$month-$day";
    } else {
        echo json_encode(['error' => 'Invalid date format']);
        exit;
    }
}

// Get reserved time slots for the date and party size
// This will check table availability for each time slot
$reserved_times = getReservedTimeSlots($date, $guests);

// Return the reserved times and available tables count for each time slot
$response = [
    'reserved_times' => $reserved_times,
    'date' => $date,
    'guests' => $guests
];

echo json_encode($response);
?>
