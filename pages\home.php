<!-- Hero Section -->
<div class="hero" style="background-image: url('<?php echo SITE_URL; ?>/assets/images/hero-bg.jpg');">
    <div class="hero-content">
        <div class="container">
            <h1>Welcome to Raymart's Diner</h1>
            <p class="lead">Experience the finest dining with our delicious menu and exceptional service</p>
            <a href="<?php echo SITE_URL; ?>/index.php?page=menu" class="btn btn-primary btn-lg">View Our Menu</a>
            <a href="<?php echo SITE_URL; ?>/index.php?page=reservation" class="btn btn-primary btn-lg ms-2">Make a Reservation</a>
        </div>
    </div>
</div>

<!-- About Section -->
<section class="about-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <img src="<?php echo SITE_URL; ?>/assets/images/restaurant.jpg" alt="About Raymart's Diner" class="img-fluid">
            </div>
            <div class="col-lg-6">
                <h2 class="mb-4">About Raymart's Diner</h2>
                <p>Welcome to Raymart's Diner, where culinary excellence meets warm hospitality. Established in 2025, our restaurant has quickly become a favorite dining destination for locals and visitors alike.</p>
                <p>At Raymart's Diner, we believe in using only the freshest ingredients to create memorable dishes that delight the senses. Our team of experienced chefs combines traditional cooking techniques with innovative approaches to deliver an exceptional dining experience.</p>
                <p>Whether you're joining us for a casual lunch, a romantic dinner, or a special celebration, we strive to make every visit to Raymart's Diner a memorable one.</p>
                <a href="<?php echo SITE_URL; ?>/index.php?page=menu" class="btn btn-primary mt-3">Explore Our Menu</a>
            </div>
        </div>
    </div>
</section>

<!-- Featured Menu Items -->
<section class="featured-menu py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Our Signature Dishes</h2>
        <div class="row">
            <?php
            // Get featured menu items (in a real application, you would have a 'featured' flag in the database)
            $stmt = $conn->prepare("SELECT * FROM menu_items ORDER BY RAND() LIMIT 3");
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                while ($item = $result->fetch_assoc()) {
                    ?>
                    <div class="col-md-4 mb-4">
                        <div class="menu-item">
                            <img src="<?php echo !empty($item['image']) ? SITE_URL . '/uploads/' . $item['image'] : SITE_URL . '/assets/images/default-food.jpg'; ?>" alt="<?php echo $item['name']; ?>" class="img-fluid">
                            <div class="menu-item-content">
                                <h5 class="menu-item-title"><?php echo $item['name']; ?></h5>
                                <div class="menu-item-price"><?php echo formatCurrency($item['price']); ?></div>
                                <p class="menu-item-description"><?php echo $item['description']; ?></p>
                                <form id="add-to-cart-form-<?php echo $item['id']; ?>" method="post" action="<?php echo SITE_URL; ?>/index.php?page=cart&action=add">
                                    <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                    <input type="hidden" name="quantity" value="1">
                                    <button type="button" class="btn btn-primary add-to-cart-btn" data-id="<?php echo $item['id']; ?>">
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                echo '<div class="col-12 text-center"><p>No menu items available yet. Check back soon!</p></div>';
            }
            ?>
        </div>
        <div class="text-center mt-4">
            <a href="<?php echo SITE_URL; ?>/index.php?page=menu" class="btn btn-outline-primary">View Full Menu</a>
        </div>
    </div>
</section>


