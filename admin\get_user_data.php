<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Check if required parameters are provided
if (!isset($_GET['type']) || !isset($_GET['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Missing parameters']);
    exit();
}

$type = sanitize($_GET['type']);
$user_id = (int)$_GET['user_id'];

// Get user data based on type
if ($type === 'orders') {
    // Get user orders
    $stmt = $conn->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY date DESC LIMIT 5");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $orders = [];
    while ($row = $result->fetch_assoc()) {
        $orders[] = $row;
    }
    
    header('Content-Type: application/json');
    echo json_encode($orders);
} else if ($type === 'reservations') {
    // Get user reservations
    $stmt = $conn->prepare("SELECT * FROM reservations WHERE user_id = ? ORDER BY date DESC, time DESC LIMIT 5");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $reservations = [];
    while ($row = $result->fetch_assoc()) {
        $reservations[] = $row;
    }
    
    header('Content-Type: application/json');
    echo json_encode($reservations);
} else {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid type']);
}
?>
