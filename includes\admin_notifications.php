<?php
// Admin notification functions for the restaurant management system

/**
 * Send notification to admin about new order
 */
function notifyAdminNewOrder($order_id, $order_details, $user) {
    global $conn;
    
    // Get admin email
    $admin_email = ADMIN_EMAIL;
    
    // Prepare email content
    $subject = 'New Order Received - Order #' . $order_id;
    $message = "A new order has been placed!\n\n";
    $message .= "Order Details:\n";
    $message .= "Order ID: #" . $order_id . "\n";
    $message .= "Customer: " . $user['name'] . " (" . $user['email'] . ")\n";
    $message .= "Order Date: " . date('F j, Y, g:i a') . "\n";
    $message .= "Payment Method: " . ucwords(str_replace('_', ' ', $order_details['payment_method'])) . "\n\n";
    
    $message .= "Items Ordered:\n";
    $total = 0;
    foreach ($order_details['items'] as $item) {
        $subtotal = $item['price'] * $item['quantity'];
        $total += $subtotal;
        $message .= "- " . $item['name'] . " x " . $item['quantity'] . " = ₱" . number_format($subtotal, 2) . "\n";
    }
    
    $message .= "\nTotal Amount: ₱" . number_format($total, 2) . "\n\n";
    $message .= "Please log in to the admin panel to manage this order.\n";
    $message .= "Admin Panel: " . SITE_URL . "/admin/\n\n";
    $message .= "This is an automated notification from " . SITE_NAME . ".";
    
    // Send email to admin
    sendEmail($admin_email, $subject, $message, null, 'admin_notification');
    
    // Log the notification
    error_log("Admin notified of new order #" . $order_id);
}

/**
 * Send notification to admin about new reservation
 */
function notifyAdminNewReservation($reservation_id, $reservation_details, $user) {
    global $conn;
    
    // Get admin email
    $admin_email = ADMIN_EMAIL;
    
    // Format table information
    $table_info = '';
    if (!empty($reservation_details['tables'])) {
        $table_numbers = array_map(function($table) {
            return "Table " . $table['id'] . " (capacity: " . $table['capacity'] . ")";
        }, $reservation_details['tables']);
        $table_info = implode(', ', $table_numbers);
    }
    
    // Prepare email content
    $subject = 'New Reservation Request - Reservation #' . $reservation_id;
    $message = "A new reservation has been made!\n\n";
    $message .= "Reservation Details:\n";
    $message .= "Reservation ID: #" . $reservation_id . "\n";
    $message .= "Customer: " . $user['name'] . " (" . $user['email'] . ")\n";
    $message .= "Date: " . formatDate($reservation_details['date']) . "\n";
    $message .= "Time: " . formatTime($reservation_details['time']) . "\n";
    $message .= "Number of Guests: " . $reservation_details['guests'] . "\n";
    
    if ($table_info) {
        $message .= "Assigned Tables: " . $table_info . "\n";
    }
    
    $message .= "Status: Pending Confirmation\n\n";
    $message .= "Please log in to the admin panel to confirm or manage this reservation.\n";
    $message .= "Admin Panel: " . SITE_URL . "/admin/\n\n";
    $message .= "This is an automated notification from " . SITE_NAME . ".";
    
    // Send email to admin
    sendEmail($admin_email, $subject, $message, null, 'admin_notification');
    
    // Log the notification
    error_log("Admin notified of new reservation #" . $reservation_id);
}

/**
 * Send notification to admin about order status change
 */
function notifyAdminOrderStatusChange($order_id, $old_status, $new_status, $user) {
    global $conn;
    
    // Only notify for certain status changes
    $notify_statuses = ['cancelled'];
    
    if (!in_array($new_status, $notify_statuses)) {
        return;
    }
    
    // Get admin email
    $admin_email = ADMIN_EMAIL;
    
    // Prepare email content
    $subject = 'Order Status Changed - Order #' . $order_id;
    $message = "An order status has been changed!\n\n";
    $message .= "Order Details:\n";
    $message .= "Order ID: #" . $order_id . "\n";
    $message .= "Customer: " . $user['name'] . " (" . $user['email'] . ")\n";
    $message .= "Previous Status: " . ucfirst($old_status) . "\n";
    $message .= "New Status: " . ucfirst($new_status) . "\n";
    $message .= "Changed At: " . date('F j, Y, g:i a') . "\n\n";
    $message .= "Please review this change in the admin panel.\n";
    $message .= "Admin Panel: " . SITE_URL . "/admin/\n\n";
    $message .= "This is an automated notification from " . SITE_NAME . ".";
    
    // Send email to admin
    sendEmail($admin_email, $subject, $message, null, 'admin_notification');
    
    // Log the notification
    error_log("Admin notified of order #" . $order_id . " status change: " . $old_status . " -> " . $new_status);
}

/**
 * Send notification to admin about reservation status change
 */
function notifyAdminReservationStatusChange($reservation_id, $old_status, $new_status, $user) {
    global $conn;
    
    // Only notify for certain status changes
    $notify_statuses = ['cancelled'];
    
    if (!in_array($new_status, $notify_statuses)) {
        return;
    }
    
    // Get admin email
    $admin_email = ADMIN_EMAIL;
    
    // Prepare email content
    $subject = 'Reservation Status Changed - Reservation #' . $reservation_id;
    $message = "A reservation status has been changed!\n\n";
    $message .= "Reservation Details:\n";
    $message .= "Reservation ID: #" . $reservation_id . "\n";
    $message .= "Customer: " . $user['name'] . " (" . $user['email'] . ")\n";
    $message .= "Previous Status: " . ucfirst($old_status) . "\n";
    $message .= "New Status: " . ucfirst($new_status) . "\n";
    $message .= "Changed At: " . date('F j, Y, g:i a') . "\n\n";
    $message .= "Please review this change in the admin panel.\n";
    $message .= "Admin Panel: " . SITE_URL . "/admin/\n\n";
    $message .= "This is an automated notification from " . SITE_NAME . ".";
    
    // Send email to admin
    sendEmail($admin_email, $subject, $message, null, 'admin_notification');
    
    // Log the notification
    error_log("Admin notified of reservation #" . $reservation_id . " status change: " . $old_status . " -> " . $new_status);
}

/**
 * Send daily summary to admin
 */
function sendDailySummaryToAdmin() {
    global $conn;
    
    $today = date('Y-m-d');
    $admin_email = ADMIN_EMAIL;
    
    // Get today's statistics
    $stmt = $conn->prepare("SELECT COUNT(*) as count, SUM(total_price) as total FROM orders WHERE DATE(date) = ? AND status != 'cancelled'");
    $stmt->bind_param("s", $today);
    $stmt->execute();
    $order_stats = $stmt->get_result()->fetch_assoc();
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM reservations WHERE date = ?");
    $stmt->bind_param("s", $today);
    $stmt->execute();
    $reservation_stats = $stmt->get_result()->fetch_assoc();
    
    // Prepare email content
    $subject = 'Daily Summary - ' . formatDate($today) . ' - ' . SITE_NAME;
    $message = "Daily Summary for " . formatDate($today) . "\n\n";
    $message .= "Orders:\n";
    $message .= "- Total Orders: " . ($order_stats['count'] ?? 0) . "\n";
    $message .= "- Total Revenue: ₱" . number_format($order_stats['total'] ?? 0, 2) . "\n\n";
    $message .= "Reservations:\n";
    $message .= "- Total Reservations: " . ($reservation_stats['count'] ?? 0) . "\n\n";
    $message .= "View detailed reports in the admin panel:\n";
    $message .= SITE_URL . "/admin/\n\n";
    $message .= "This is an automated daily summary from " . SITE_NAME . ".";
    
    // Send email to admin
    sendEmail($admin_email, $subject, $message, null, 'admin_notification');
    
    // Log the notification
    error_log("Daily summary sent to admin for " . $today);
}
?>
