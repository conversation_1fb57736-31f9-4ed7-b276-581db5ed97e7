<?php
// Get user information
$user = getUserById($_SESSION['user_id']);

// Handle profile update form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $name = sanitize($_POST['name']);
    $phone = sanitize($_POST['phone']);
    $address = sanitize($_POST['address']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate form data
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Name is required!';
    }

    // Check if user wants to change password
    if (!empty($current_password) || !empty($new_password) || !empty($confirm_password)) {
        if (empty($current_password)) {
            $errors[] = 'Current password is required to change password!';
        } else if (!password_verify($current_password, $user['password'])) {
            $errors[] = 'Current password is incorrect!';
        }

        if (empty($new_password)) {
            $errors[] = 'New password is required!';
        } else if (strlen($new_password) < 6) {
            $errors[] = 'New password must be at least 6 characters long!';
        }

        if ($new_password != $confirm_password) {
            $errors[] = 'New passwords do not match!';
        }
    }

    // If no errors, update profile
    if (empty($errors)) {
        // Update user information
        if (!empty($new_password)) {
            // Hash new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

            // Update user with new password
            $stmt = $conn->prepare("UPDATE users SET name = ?, phone = ?, address = ?, password = ? WHERE id = ?");
            $stmt->bind_param("ssssi", $name, $phone, $address, $hashed_password, $_SESSION['user_id']);
        } else {
            // Update user without changing password
            $stmt = $conn->prepare("UPDATE users SET name = ?, phone = ?, address = ? WHERE id = ?");
            $stmt->bind_param("sssi", $name, $phone, $address, $_SESSION['user_id']);
        }

        if ($stmt->execute()) {
            // Set success message
            $_SESSION['message'] = 'Profile updated successfully!';
            $_SESSION['message_type'] = 'success';

            // Redirect to refresh page
            redirect('index.php?page=profile');
        } else {
            // Set error message
            $_SESSION['message'] = 'Failed to update profile. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    } else {
        // Set error message
        $_SESSION['message'] = implode('<br>', $errors);
        $_SESSION['message_type'] = 'danger';
    }
}

// Get user orders
$orders = getUserOrders($_SESSION['user_id']);

// Get user reservations
$reservations = getUserReservations($_SESSION['user_id']);
?>

<div class="container">
    <h1 class="mb-4">My Profile</h1>

    <div class="row">
        <div class="col-lg-4 mb-4">
            <!-- Profile Information -->
            <div class="profile-card">
                <h4 class="mb-4">Profile Information</h4>

                <form method="post" action="<?php echo SITE_URL; ?>/index.php?page=profile">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" required value="<?php echo $user['name']; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" value="<?php echo $user['email']; ?>" disabled>
                        <small class="text-muted">Email cannot be changed</small>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo $user['phone']; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo $user['address']; ?></textarea>
                    </div>

                    <h5 class="mt-4 mb-3">Change Password</h5>

                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password">
                    </div>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password">
                        <small class="text-muted">Leave blank to keep current password</small>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" name="update_profile">Update Profile</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Order History -->
            <div class="profile-card">
                <h4 class="mb-4">Order History</h4>

                <?php if (empty($orders)): ?>
                    <div class="alert alert-info">
                        You haven't placed any orders yet. <a href="<?php echo SITE_URL; ?>/index.php?page=menu">Browse our menu</a> to place an order.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>#<?php echo $order['id']; ?></td>
                                        <td><?php echo formatDate($order['date']); ?></td>
                                        <td>
                                            <?php
                                            $items = json_decode($order['items'], true);
                                            $item_count = count($items);
                                            echo $item_count . ' item' . ($item_count > 1 ? 's' : '');
                                            ?>
                                            <button type="button" class="btn btn-sm btn-link" data-bs-toggle="modal" data-bs-target="#orderModal<?php echo $order['id']; ?>">
                                                View Details
                                            </button>
                                        </td>
                                        <td><?php echo formatCurrency($order['total_price']); ?></td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            switch ($order['status']) {
                                                case 'pending':
                                                    $status_class = 'bg-warning';
                                                    break;
                                                case 'preparing':
                                                    $status_class = 'bg-info';
                                                    break;
                                                case 'completed':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'cancelled':
                                                    $status_class = 'bg-danger';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($order['status']); ?></span>
                                        </td>
                                    </tr>

                                    <!-- Order Details Modal -->
                                    <div class="modal fade" id="orderModal<?php echo $order['id']; ?>" tabindex="-1" aria-labelledby="orderModalLabel<?php echo $order['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="orderModalLabel<?php echo $order['id']; ?>">Order #<?php echo $order['id']; ?> Details</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p><strong>Date:</strong> <?php echo formatDate($order['date']); ?></p>
                                                    <p><strong>Status:</strong> <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($order['status']); ?></span></p>

                                                    <h6 class="mt-4">Items:</h6>
                                                    <ul class="list-group mb-3">
                                                        <?php
                                                        $items = json_decode($order['items'], true);
                                                        foreach ($items as $item):
                                                        ?>
                                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                <?php echo $item['name']; ?> x <?php echo $item['quantity']; ?>
                                                                <span><?php echo formatCurrency($item['price'] * $item['quantity']); ?></span>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ul>

                                                    <div class="d-flex justify-content-between">
                                                        <h6>Total:</h6>
                                                        <h6><?php echo formatCurrency($order['total_price']); ?></h6>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-primary" onclick="generatePDF(<?php echo $order['id']; ?>)">
                                                        <i class="fas fa-file-pdf"></i> Download Receipt PDF
                                                    </button>
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Reservation History -->
            <div class="profile-card mt-4">
                <h4 class="mb-4">Reservation History</h4>

                <?php if (empty($reservations)): ?>
                    <div class="alert alert-info">
                        You haven't made any reservations yet. <a href="<?php echo SITE_URL; ?>/index.php?page=reservation">Make a reservation</a> now.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Reservation ID</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Guests</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reservations as $reservation): ?>
                                    <tr>
                                        <td>#<?php echo $reservation['id']; ?></td>
                                        <td><?php echo formatDate($reservation['date']); ?></td>
                                        <td><?php echo formatTime($reservation['time']); ?></td>
                                        <td><?php echo $reservation['guests']; ?></td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            switch ($reservation['status']) {
                                                case 'pending':
                                                    $status_class = 'bg-warning';
                                                    break;
                                                case 'confirmed':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'cancelled':
                                                    $status_class = 'bg-danger';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($reservation['status']); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
