<div class="container">
    <h1 class="mb-4">Our Menu</h1>
    
    <!-- Category Filter -->
    <div class="category-pills d-flex flex-wrap">
        <div class="category-pill active" data-category="all">All</div>
        <?php
        // Get all categories
        $categories = getMenuCategories();
        
        foreach ($categories as $category) {
            echo '<div class="category-pill" data-category="' . $category . '">' . ucfirst($category) . '</div>';
        }
        ?>
    </div>
    
    <!-- Menu Items -->
    <div class="row">
        <?php
        // Get all menu items
        $menu_items = getMenuItems();
        
        if (count($menu_items) > 0) {
            foreach ($menu_items as $item) {
                ?>
                <div class="col-md-4 col-sm-6 menu-item" data-category="<?php echo $item['category']; ?>">
                    <div class="card h-100 shadow-sm">
                        <img src="<?php echo !empty($item['image']) ? SITE_URL . '/uploads/' . $item['image'] : SITE_URL . '/assets/images/default-food.jpg'; ?>" alt="<?php echo $item['name']; ?>" class="card-img-top">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $item['name']; ?></h5>
                            <p class="card-text menu-item-price"><?php echo formatCurrency($item['price']); ?></p>
                            <p class="card-text"><?php echo $item['description']; ?></p>
                            <form id="add-to-cart-form-<?php echo $item['id']; ?>" method="post" action="<?php echo SITE_URL; ?>/index.php?page=cart&action=add">
                                <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                <div class="d-flex align-items-center mb-3">
                                    <label for="quantity-<?php echo $item['id']; ?>" class="me-2">Quantity:</label>
                                    <div class="input-group" style="width: 120px;">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="decrementQuantity(<?php echo $item['id']; ?>)">-</button>
                                        <input type="number" id="quantity-<?php echo $item['id']; ?>" name="quantity" class="form-control form-control-sm text-center" value="1" min="1">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="incrementQuantity(<?php echo $item['id']; ?>)">+</button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary add-to-cart-btn" data-id="<?php echo $item['id']; ?>">
                                    <i class="fas fa-cart-plus"></i> Add to Cart
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <?php
            }
        } else {
            echo '<div class="col-12 text-center"><p>No menu items available yet. Check back soon!</p></div>';
        }
        ?>
    </div>
</div>

<script>
    function decrementQuantity(itemId) {
        const input = document.getElementById('quantity-' + itemId);
        if (input.value > 1) {
            input.value = parseInt(input.value) - 1;
        }
    }
    
    function incrementQuantity(itemId) {
        const input = document.getElementById('quantity-' + itemId);
        input.value = parseInt(input.value) + 1;
    }
</script>
