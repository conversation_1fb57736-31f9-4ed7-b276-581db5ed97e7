<?php
/**
 * CLEANUP SCRIPT - Remove old denormalized columns
 * 
 * WARNING: This script will remove the old denormalized columns.
 * Only run this after thoroughly testing the normalized structure.
 * Make sure to backup your database before running this script.
 * 
 * This script should be run manually after confirming everything works correctly.
 */

include 'config/config.php';

echo "<h1>Database Cleanup - Remove Old Denormalized Columns</h1>\n";
echo "<p><strong>WARNING:</strong> This will permanently remove old columns. Make sure you have a backup!</p>\n";

// Confirmation check
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<p><a href='?confirm=yes' style='color: red; font-weight: bold;'>Click here to confirm and proceed with cleanup</a></p>\n";
    echo "<p>This will remove the following columns:</p>\n";
    echo "<ul>\n";
    echo "<li>orders.items (JSON field - replaced by order_items table)</li>\n";
    echo "<li>orders.payment_method (ENUM - replaced by payment_method_id)</li>\n";
    echo "<li>orders.status (ENUM - replaced by status_id)</li>\n";
    echo "<li>reservations.status (ENUM - replaced by status_id)</li>\n";
    echo "<li>email_logs.type (ENUM - replaced by type_id)</li>\n";
    echo "<li>payment_transactions.payment_method (ENUM - replaced by payment_method_id)</li>\n";
    echo "<li>payment_transactions.transaction_status (ENUM - replaced by transaction_status_id)</li>\n";
    echo "<li>menu_items.category (VARCHAR - replaced by category_id)</li>\n";
    echo "</ul>\n";
    exit;
}

echo "<h2>Starting Cleanup Process...</h2>\n";

// Array of columns to remove
$columns_to_remove = [
    'orders' => ['items', 'payment_method', 'status'],
    'reservations' => ['status'],
    'email_logs' => ['type'],
    'payment_transactions' => ['payment_method', 'transaction_status'],
    'menu_items' => ['category']
];

$success_count = 0;
$error_count = 0;

foreach ($columns_to_remove as $table => $columns) {
    echo "<h3>Processing table: $table</h3>\n";
    
    foreach ($columns as $column) {
        echo "<p>Removing column: $table.$column ... ";
        
        // Check if column exists first
        $check_sql = "SHOW COLUMNS FROM $table LIKE '$column'";
        $result = $conn->query($check_sql);
        
        if ($result->num_rows > 0) {
            // Column exists, remove it
            $drop_sql = "ALTER TABLE $table DROP COLUMN $column";
            
            if ($conn->query($drop_sql) === TRUE) {
                echo "<span style='color: green;'>✓ Success</span></p>\n";
                $success_count++;
            } else {
                echo "<span style='color: red;'>✗ Error: " . $conn->error . "</span></p>\n";
                $error_count++;
            }
        } else {
            echo "<span style='color: orange;'>⚠ Column doesn't exist (already removed?)</span></p>\n";
        }
    }
}

echo "<h2>Cleanup Summary</h2>\n";
echo "<p>Successful removals: <strong>$success_count</strong></p>\n";
echo "<p>Errors: <strong>$error_count</strong></p>\n";

if ($error_count === 0) {
    echo "<p style='color: green; font-weight: bold;'>✅ Cleanup completed successfully!</p>\n";
    echo "<p>The database is now fully normalized with old denormalized columns removed.</p>\n";
    
    // Update any remaining code that might reference old columns
    echo "<h3>Next Steps:</h3>\n";
    echo "<ul>\n";
    echo "<li>✅ Test all functionality thoroughly</li>\n";
    echo "<li>✅ Update any remaining code that references old columns</li>\n";
    echo "<li>✅ Update documentation</li>\n";
    echo "<li>✅ Consider removing this cleanup script</li>\n";
    echo "</ul>\n";
    
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Some errors occurred during cleanup.</p>\n";
    echo "<p>Please review the errors above and fix them manually if needed.</p>\n";
}

// Show final table structures
echo "<h2>Final Table Structures</h2>\n";

$tables_to_show = ['orders', 'reservations', 'email_logs', 'payment_transactions', 'menu_items'];

foreach ($tables_to_show as $table) {
    echo "<h3>$table</h3>\n";
    $result = $conn->query("DESCRIBE $table");
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
}

echo "<h2>🎉 Database Normalization Complete!</h2>\n";
echo "<p>Your restaurant management system database is now fully normalized to 3NF+ standards.</p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

h1, h2, h3 {
    color: #333;
}

.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}
</style>
