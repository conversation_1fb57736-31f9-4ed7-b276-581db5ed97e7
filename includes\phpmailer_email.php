<?php
/**
 * PHPMailer Email Integration
 *
 * This file contains functions for sending emails via PHPMailer.
 */

//Import PHPMailer classes into the global namespace
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

//Required files
require_once __DIR__ . '/../phpmailer/src/Exception.php';
require_once __DIR__ . '/../phpmailer/src/PHPMailer.php';
require_once __DIR__ . '/../phpmailer/src/SMTP.php';

// Email configuration
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_USERNAME', '<EMAIL>'); // Your Gmail address
define('MAIL_PASSWORD', 'bozazwqygamnhfib'); // Your Gmail app password
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'Ray<PERSON>\'s Diner');
define('MAIL_PORT', 465);
define('MAIL_ENCRYPTION', 'ssl');

/**
 * Send an email using PHPMailer
 *
 * @param string $to_email Recipient email
 * @param string $to_name Recipient name
 * @param string $subject Email subject
 * @param string $text_body Plain text email body
 * @param string $html_body HTML email body (optional)
 * @return bool True if email sent successfully, false otherwise
 */
function sendWithPHPMailer($to_email, $to_name, $subject, $text_body, $html_body = '') {
    // If no HTML body is provided, create a simple one from the text body
    if (empty($html_body)) {
        $html_body = nl2br($text_body);
    }

    try {
        // Create a new PHPMailer instance
        $mail = new PHPMailer(true);

        // Debug settings - uncomment for debugging
        // $mail->SMTPDebug = 2; // Enable verbose debug output
        // $mail->Debugoutput = 'html';

        // Server settings
        $mail->isSMTP();
        $mail->Host = MAIL_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = MAIL_USERNAME;
        $mail->Password = MAIL_PASSWORD;
        $mail->SMTPSecure = MAIL_ENCRYPTION;
        $mail->Port = MAIL_PORT;

        // Set timeout values
        $mail->Timeout = 60; // Timeout in seconds

        // Recipients
        $mail->setFrom(MAIL_FROM_EMAIL, MAIL_FROM_NAME);
        $mail->addAddress($to_email, $to_name);
        $mail->addReplyTo(MAIL_FROM_EMAIL, MAIL_FROM_NAME);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $html_body;
        $mail->AltBody = $text_body;

        // Set character set
        $mail->CharSet = 'UTF-8';

        // Send the email
        if (!$mail->send()) {
            throw new Exception($mail->ErrorInfo);
        }

        return true;
    } catch (Exception $e) {
        // Log the detailed error
        error_log("PHPMailer Error: " . $e->getMessage());

        // Save the email to a file as a fallback
        return saveEmailToFile($to_email, $to_name, $subject, $text_body, $html_body);
    }
}

/**
 * Save email to a file (fallback method)
 *
 * @param string $to_email Recipient email
 * @param string $to_name Recipient name
 * @param string $subject Email subject
 * @param string $text_body Plain text email body
 * @param string $html_body HTML email body
 * @return bool True if email saved successfully, false otherwise
 */
function saveEmailToFile($to_email, $to_name, $subject, $text_body, $html_body) {
    try {
        // Create a unique boundary for multipart message
        $boundary = md5(time());

        // Prepare headers
        $headers = "From: " . MAIL_FROM_NAME . " <" . MAIL_FROM_EMAIL . ">\r\n";
        $headers .= "Reply-To: " . MAIL_FROM_EMAIL . "\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: multipart/alternative; boundary=\"" . $boundary . "\"\r\n";

        // Prepare message body
        $body = "--" . $boundary . "\r\n";
        $body .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $body .= $text_body . "\r\n\r\n";

        if (!empty($html_body)) {
            $body .= "--" . $boundary . "\r\n";
            $body .= "Content-Type: text/html; charset=UTF-8\r\n";
            $body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
            $body .= $html_body . "\r\n\r\n";
        }

        $body .= "--" . $boundary . "--";

        // Create emails directory if it doesn't exist
        $email_dir = __DIR__ . '/../emails';
        if (!file_exists($email_dir)) {
            if (!mkdir($email_dir, 0777, true)) {
                error_log("Failed to create directory: " . $email_dir);
                return false;
            }
        }

        // Create a unique filename
        $filename = $email_dir . '/email_' . time() . '_' . md5($to_email . $subject) . '.eml';

        // Write the email content to the file
        $email_content = "To: " . $to_name . " <" . $to_email . ">\r\n";
        $email_content .= "Subject: " . $subject . "\r\n";
        $email_content .= $headers . "\r\n";
        $email_content .= $body;

        // Log that we're saving to a file
        error_log("Saving email to file: " . $filename);

        if (file_put_contents($filename, $email_content) === false) {
            error_log("Failed to write email to file: " . $filename);
            return false;
        }

        error_log("Email saved successfully to file: " . $filename);
        return true;
    } catch (Exception $e) {
        error_log("Email file save error: " . $e->getMessage());
        return false;
    }
}
?>
