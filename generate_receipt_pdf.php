<?php
session_start();
include 'config/config.php';
include 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'You must be logged in to access this feature.']);
    exit;
}

// Check if order ID is provided
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Order ID is required.']);
    exit;
}

$order_id = (int)$_GET['order_id'];

// Get order details
$stmt = $conn->prepare("SELECT o.*, u.name as user_name, u.email as user_email
                        FROM orders o
                        JOIN users u ON o.user_id = u.id
                        WHERE o.id = ? AND (o.user_id = ? OR ? IN (SELECT id FROM users WHERE is_admin = 1))");
$stmt->bind_param("iii", $order_id, $_SESSION['user_id'], $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Order not found or you do not have permission to view it.']);
    exit;
}

$order = $result->fetch_assoc();
$items = json_decode($order['items'], true);

// Calculate total
$total = 0;
foreach ($items as $item) {
    $total += $item['price'] * $item['quantity'];
}

// Include TCPDF library
require_once('tcpdf/tcpdf.php');

// Create new PDF document
$pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

// Set document information
$pdf->SetCreator(SITE_NAME);
$pdf->SetAuthor(SITE_NAME);
$pdf->SetTitle('Receipt #' . $order_id);
$pdf->SetSubject('Receipt for Order #' . $order_id);

// Ensure proper encoding for special characters like the peso symbol
$pdf->setEncoding('UTF-8');

// Set font subsetting to improve font handling
$pdf->setFontSubsetting(true);

// Remove header and footer
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);

// Set default monospaced font
$pdf->SetDefaultMonospacedFont('courier');

// Set margins
$pdf->SetMargins(15, 15, 15);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 15);

// Set image scale factor
$pdf->setImageScale(1.25);

// Add a page
$pdf->AddPage();

// Set font with Unicode support
$pdf->SetFont('helvetica', '', 10, '', true);

// Logo and header
$pdf->SetFont('helvetica', 'B', 16, '', true);
$pdf->Cell(0, 10, SITE_NAME, 0, 1, 'C');
$pdf->SetFont('helvetica', '', 10, '', true);
$pdf->Cell(0, 5, 'Purok 6A, Tagbuyacan, Santiago, Agusan del Norte', 0, 1, 'C');
$pdf->Cell(0, 5, 'Phone: +63 ************ | Email: <EMAIL>', 0, 1, 'C');
$pdf->Ln(5);

// Receipt title
$pdf->SetFont('helvetica', 'B', 14, '', true);
$pdf->Cell(0, 10, 'RECEIPT', 0, 1, 'C');
$pdf->Ln(5);

// Order information
$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(40, 7, 'Receipt No:', 0, 0);
$pdf->SetFont('helvetica', '', 11, '', true);
$pdf->Cell(0, 7, '#' . $order_id, 0, 1);

$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(40, 7, 'Date:', 0, 0);
$pdf->SetFont('helvetica', '', 11, '', true);
$pdf->Cell(0, 7, date('F j, Y, g:i a', strtotime($order['date'])), 0, 1);

$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(40, 7, 'Customer:', 0, 0);
$pdf->SetFont('helvetica', '', 11, '', true);
$pdf->Cell(0, 7, $order['user_name'], 0, 1);

$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(40, 7, 'Email:', 0, 0);
$pdf->SetFont('helvetica', '', 11, '', true);
$pdf->Cell(0, 7, $order['user_email'], 0, 1);

$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(40, 7, 'Payment Method:', 0, 0);
$pdf->SetFont('helvetica', '', 11, '', true);
$pdf->Cell(0, 7, ucwords(str_replace('_', ' ', $order['payment_method'])), 0, 1);

$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(40, 7, 'Status:', 0, 0);
$pdf->SetFont('helvetica', '', 11, '', true);
$pdf->Cell(0, 7, ucfirst($order['status']), 0, 1);
$pdf->Ln(5);

// Items table header
$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(80, 7, 'Item', 1, 0, 'L', 1);
$pdf->Cell(30, 7, 'Price', 1, 0, 'R', 1);
$pdf->Cell(30, 7, 'Quantity', 1, 0, 'C', 1);
$pdf->Cell(40, 7, 'Subtotal', 1, 1, 'R', 1);

// Items
$pdf->SetFont('helvetica', '', 10, '', true);
foreach ($items as $item) {
    $itemSubtotal = $item['price'] * $item['quantity'];
    $pdf->Cell(80, 7, $item['name'], 1, 0, 'L');
    $pdf->Cell(30, 7, 'P ' . formatCurrency($item['price']), 1, 0, 'R');
    $pdf->Cell(30, 7, $item['quantity'], 1, 0, 'C');
    $pdf->Cell(40, 7, 'P ' . formatCurrency($itemSubtotal), 1, 1, 'R');
}

// Total
$pdf->SetFont('helvetica', 'B', 11, '', true);
$pdf->Cell(140, 7, 'Total:', 1, 0, 'R');
$pdf->Cell(40, 7, 'P ' . formatCurrency($total), 1, 1, 'R');

// Thank you message
$pdf->Ln(10);
$pdf->SetFont('helvetica', '', 10, '', true);
$pdf->Cell(0, 7, 'Thank you for dining with us!', 0, 1, 'C');
$pdf->Cell(0, 7, 'Please come again.', 0, 1, 'C');

// Output the PDF
$pdf_filename = 'receipt_' . $order_id . '_' . date('Ymd') . '.pdf';

// Check if we need to save the PDF to the server
if (isset($_GET['save']) && $_GET['save'] == 1) {
    // Create uploads/receipts directory if it doesn't exist
    if (!file_exists('uploads/receipts')) {
        mkdir('uploads/receipts', 0777, true);
    }

    $file_path = 'uploads/receipts/' . $pdf_filename;
    $pdf->Output($file_path, 'F');

    // Save receipt record in database
    $stmt = $conn->prepare("INSERT INTO receipts (order_id, file_path) VALUES (?, ?) ON DUPLICATE KEY UPDATE file_path = ?");
    $stmt->bind_param("iss", $order_id, $file_path, $file_path);
    $stmt->execute();

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'Receipt saved successfully.', 'file_path' => $file_path]);
    exit;
} else {
    // Output PDF for download
    $pdf->Output($pdf_filename, 'D');
}
?>
