<?php
ob_start();
session_start();
include 'config/config.php';
include 'includes/functions.php';

if (strpos($_SERVER['REQUEST_URI'], 'admin') !== false && !isset($_SESSION['admin_id'])) {
    header("Location: admin/login.php");
    exit();
}

$page = isset($_GET['page']) ? $_GET['page'] : 'home';

include 'includes/header.php';

switch ($page) {
    case 'home':
        include 'pages/home.php';
        break;
    case 'menu':
        include 'pages/menu.php';
        break;
    case 'cart':
        include 'pages/cart.php';
        break;
    case 'reservation':
        include 'pages/reservation.php';
        break;
    case 'profile':
        if (!isset($_SESSION['user_id'])) {
            header("Location: index.php?page=login");
            exit();
        }
        include 'pages/profile.php';
        break;
    case 'login':
        include 'pages/login.php';
        break;
    case 'register':
        include 'pages/register.php';
        break;
    case 'logout':
        session_destroy();
        header("Location: index.php");
        exit();
        break;
    default:
        include 'pages/home.php';
        break;
}

include 'includes/footer.php';

ob_end_flush();
?>
