<?php
/**
 * Email Template Functions
 * 
 * This file contains functions for generating HTML email templates.
 */

/**
 * Generate a basic HTML email template
 * 
 * @param string $title Email title
 * @param string $content Email content (can include HTML)
 * @param string $footer Email footer text
 * @return string HTML email template
 */
function getBasicEmailTemplate($title, $content, $footer = '') {
    // If no footer is provided, use a default one
    if (empty($footer)) {
        $footer = 'Thank you for choosing ' . SITE_NAME . '!';
    }
    
    // Get the current year for the copyright notice
    $year = date('Y');
    
    // Build the HTML template
    $html = '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($title) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eeeeee;
        }
        .header h1 {
            color: #333333;
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #eeeeee;
            color: #777777;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>' . htmlspecialchars($title) . '</h1>
        </div>
        <div class="content">
            ' . $content . '
        </div>
        <div class="footer">
            <p>' . htmlspecialchars($footer) . '</p>
            <p>&copy; ' . $year . ' ' . SITE_NAME . '. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    
    return $html;
}

/**
 * Generate an order confirmation email template
 * 
 * @param array $order Order details
 * @param array $order_items Order items
 * @param array $user User details
 * @return string HTML email template
 */
function getOrderConfirmationTemplate($order, $order_items, $user) {
    // Format the order date
    $order_date = date('m/d/Y h:i A', strtotime($order['created_at']));
    
    // Calculate the total
    $total = 0;
    foreach ($order_items as $item) {
        $total += $item['price'] * $item['quantity'];
    }
    
    // Build the items table
    $items_html = '
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr style="background-color: #f2f2f2;">
            <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Item</th>
            <th style="padding: 10px; text-align: right; border: 1px solid #ddd;">Price</th>
            <th style="padding: 10px; text-align: center; border: 1px solid #ddd;">Qty</th>
            <th style="padding: 10px; text-align: right; border: 1px solid #ddd;">Subtotal</th>
        </tr>';
    
    foreach ($order_items as $item) {
        $subtotal = $item['price'] * $item['quantity'];
        $items_html .= '
        <tr>
            <td style="padding: 10px; text-align: left; border: 1px solid #ddd;">' . htmlspecialchars($item['name']) . '</td>
            <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">₱' . number_format($item['price'], 2) . '</td>
            <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">' . $item['quantity'] . '</td>
            <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">₱' . number_format($subtotal, 2) . '</td>
        </tr>';
    }
    
    $items_html .= '
        <tr>
            <td colspan="3" style="padding: 10px; text-align: right; border: 1px solid #ddd;"><strong>Total</strong></td>
            <td style="padding: 10px; text-align: right; border: 1px solid #ddd;"><strong>₱' . number_format($total, 2) . '</strong></td>
        </tr>
    </table>';
    
    // Build the content
    $content = '
    <p>Dear ' . htmlspecialchars($user['name']) . ',</p>
    
    <p>Thank you for your order! We\'re pleased to confirm that your order has been received and is being processed.</p>
    
    <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
        <p><strong>Order Number:</strong> #' . $order['id'] . '</p>
        <p><strong>Order Date:</strong> ' . $order_date . '</p>
        <p><strong>Payment Method:</strong> ' . ucfirst($order['payment_method']) . '</p>
        <p><strong>Order Status:</strong> ' . ucfirst($order['status']) . '</p>
    </div>
    
    <h3>Order Details</h3>
    ' . $items_html . '
    
    <p>If you have any questions about your order, please contact us at <a href="mailto:' . ADMIN_EMAIL . '">' . ADMIN_EMAIL . '</a>.</p>
    
    <div class="text-center">
        <p>We appreciate your business!</p>
    </div>';
    
    return getBasicEmailTemplate('Order Confirmation - #' . $order['id'], $content);
}

/**
 * Generate a reservation confirmation email template
 * 
 * @param array $reservation Reservation details
 * @param array $user User details
 * @return string HTML email template
 */
function getReservationConfirmationTemplate($reservation, $user) {
    // Format the reservation date and time
    $reservation_date = date('m/d/Y', strtotime($reservation['date']));
    $reservation_time = date('h:i A', strtotime($reservation['time']));
    
    // Build the content
    $content = '
    <p>Dear ' . htmlspecialchars($user['name']) . ',</p>
    
    <p>Thank you for your reservation! We\'re pleased to confirm that your table has been reserved.</p>
    
    <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">
        <p><strong>Reservation Number:</strong> #' . $reservation['id'] . '</p>
        <p><strong>Date:</strong> ' . $reservation_date . '</p>
        <p><strong>Time:</strong> ' . $reservation_time . '</p>
        <p><strong>Number of Guests:</strong> ' . $reservation['guests'] . '</p>
        <p><strong>Table Number:</strong> ' . $reservation['table_id'] . '</p>
        <p><strong>Special Requests:</strong> ' . (empty($reservation['special_requests']) ? 'None' : htmlspecialchars($reservation['special_requests'])) . '</p>
    </div>
    
    <p>If you need to modify or cancel your reservation, please contact us at <a href="mailto:' . ADMIN_EMAIL . '">' . ADMIN_EMAIL . '</a> or call us at ' . SITE_PHONE . '.</p>
    
    <div class="text-center">
        <p>We look forward to serving you!</p>
    </div>';
    
    return getBasicEmailTemplate('Reservation Confirmation - #' . $reservation['id'], $content);
}
?>
