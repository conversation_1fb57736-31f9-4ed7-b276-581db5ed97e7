<?php
// Test script to verify database normalization
include 'config/config.php';
include 'includes/functions.php';

echo "<h1>Database Normalization Test</h1>\n";

// Test 1: Check if reference tables exist and have data
echo "<h2>1. Reference Tables Check</h2>\n";

$tables = ['categories', 'payment_methods', 'order_statuses', 'reservation_statuses', 'email_types', 'transaction_statuses'];

foreach ($tables as $table) {
    $result = $conn->query("SELECT COUNT(*) as count FROM $table");
    $row = $result->fetch_assoc();
    echo "<p>✓ $table: {$row['count']} records</p>\n";
}

// Test 2: Check if foreign key columns exist
echo "<h2>2. Foreign Key Columns Check</h2>\n";

$fk_checks = [
    'menu_items' => 'category_id',
    'orders' => ['payment_method_id', 'status_id'],
    'reservations' => 'status_id',
    'email_logs' => 'type_id',
    'payment_transactions' => ['payment_method_id', 'transaction_status_id']
];

foreach ($fk_checks as $table => $columns) {
    if (is_array($columns)) {
        foreach ($columns as $column) {
            $result = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($result->num_rows > 0) {
                echo "<p>✓ $table.$column exists</p>\n";
            } else {
                echo "<p>✗ $table.$column missing</p>\n";
            }
        }
    } else {
        $result = $conn->query("SHOW COLUMNS FROM $table LIKE '$columns'");
        if ($result->num_rows > 0) {
            echo "<p>✓ $table.$columns exists</p>\n";
        } else {
            echo "<p>✗ $table.$columns missing</p>\n";
        }
    }
}

// Test 3: Check data migration
echo "<h2>3. Data Migration Check</h2>\n";

// Check if menu items have category_id populated
$result = $conn->query("SELECT COUNT(*) as total, COUNT(category_id) as with_category_id FROM menu_items");
$row = $result->fetch_assoc();
echo "<p>Menu Items: {$row['with_category_id']}/{$row['total']} have category_id populated</p>\n";

// Check if orders have payment_method_id and status_id populated
$result = $conn->query("SELECT COUNT(*) as total, COUNT(payment_method_id) as with_payment_id, COUNT(status_id) as with_status_id FROM orders");
$row = $result->fetch_assoc();
echo "<p>Orders: {$row['with_payment_id']}/{$row['total']} have payment_method_id, {$row['with_status_id']}/{$row['total']} have status_id</p>\n";

// Test 4: Test normalized functions
echo "<h2>4. Function Tests</h2>\n";

try {
    $categories = getMenuCategories();
    echo "<p>✓ getMenuCategories(): " . count($categories) . " categories</p>\n";
    
    $payment_methods = getPaymentMethods();
    echo "<p>✓ getPaymentMethods(): " . count($payment_methods) . " methods</p>\n";
    
    $order_statuses = getOrderStatuses();
    echo "<p>✓ getOrderStatuses(): " . count($order_statuses) . " statuses</p>\n";
    
    $menu_items = getMenuItems();
    echo "<p>✓ getMenuItems(): " . count($menu_items) . " items</p>\n";
    
} catch (Exception $e) {
    echo "<p>✗ Function test failed: " . $e->getMessage() . "</p>\n";
}

// Test 5: Check foreign key constraints
echo "<h2>5. Foreign Key Constraints Check</h2>\n";

$constraints = [
    'fk_menu_items_category',
    'fk_orders_payment_method',
    'fk_orders_status',
    'fk_reservations_status',
    'fk_email_logs_type',
    'fk_payment_transactions_payment_method',
    'fk_payment_transactions_status'
];

foreach ($constraints as $constraint) {
    $result = $conn->query("SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE CONSTRAINT_SCHEMA = DATABASE() AND CONSTRAINT_NAME = '$constraint'");
    if ($result->num_rows > 0) {
        echo "<p>✓ $constraint exists</p>\n";
    } else {
        echo "<p>✗ $constraint missing</p>\n";
    }
}

// Test 6: Sample data display
echo "<h2>6. Sample Data Display</h2>\n";

echo "<h3>Categories:</h3>\n";
$result = $conn->query("SELECT * FROM categories ORDER BY display_order");
echo "<table border='1'><tr><th>ID</th><th>Name</th><th>Display Order</th><th>Active</th></tr>\n";
while ($row = $result->fetch_assoc()) {
    echo "<tr><td>{$row['id']}</td><td>{$row['name']}</td><td>{$row['display_order']}</td><td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td></tr>\n";
}
echo "</table>\n";

echo "<h3>Payment Methods:</h3>\n";
$result = $conn->query("SELECT * FROM payment_methods ORDER BY display_order");
echo "<table border='1'><tr><th>ID</th><th>Code</th><th>Name</th><th>Active</th></tr>\n";
while ($row = $result->fetch_assoc()) {
    echo "<tr><td>{$row['id']}</td><td>{$row['code']}</td><td>{$row['name']}</td><td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td></tr>\n";
}
echo "</table>\n";

echo "<h3>Sample Menu Items with Categories:</h3>\n";
$result = $conn->query("SELECT mi.id, mi.name, mi.category, c.name as category_name, mi.price FROM menu_items mi LEFT JOIN categories c ON mi.category_id = c.id LIMIT 5");
echo "<table border='1'><tr><th>ID</th><th>Name</th><th>Old Category</th><th>New Category</th><th>Price</th></tr>\n";
while ($row = $result->fetch_assoc()) {
    echo "<tr><td>{$row['id']}</td><td>{$row['name']}</td><td>{$row['category']}</td><td>{$row['category_name']}</td><td>₱{$row['price']}</td></tr>\n";
}
echo "</table>\n";

echo "<h2>✅ Database Normalization Test Complete!</h2>\n";
echo "<p>The database has been successfully normalized to 3NF+ with proper relationships and foreign key constraints.</p>\n";
?>
