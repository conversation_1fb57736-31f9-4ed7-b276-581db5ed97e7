# RAYMART'S DINER - Restaurant Management System

A complete restaurant management system for RAYMART'S DINER with client-side and admin-side features.

## Features

### Client-Side Features
- **Home Page**: Welcome banner and about section
- **Menu Page**: Display menu items with filtering by category
- **Order System**: Real-time cart summary and order placement
- **Reservation System**: Table reservation with availability checking
- **Authentication**: User sign-up, login, and profile management
- **Email Notification**: Confirmation emails for orders and reservations

### Admin-Side Features
- **Dashboard**: Sales summary, reservation stats, and charts
- **Menu Management**: Add, edit, and delete menu items
- **Order Management**: View and update order status
- **Reservation Management**: Manage table reservations
- **Table Availability Tracker**: Real-time table availability
- **User Management**: View and manage registered users

## Technologies Used
- **Frontend**: HTML, CSS, JavaScript, Bootstrap 5
- **Backend**: PHP
- **Database**: MySQL
- **Charts**: Chart.js
- **Icons**: Font Awesome

## Installation

### Prerequisites
- XAMPP (or any PHP server with MySQL)
- Web browser

### Setup Instructions
1. Clone the repository to your XAMPP htdocs folder:
   ```
   git clone https://github.com/yourusername/restaurant-management-system.git
   ```

2. Start Apache and MySQL services in XAMPP.

3. Open your web browser and navigate to:
   ```
   http://localhost/restaurant-management-system3/
   ```

4. The system will automatically create the database and required tables on first run.

5. Default admin credentials:
   - Username: admin
   - Password: admin123

## Directory Structure
```
restaurant-management-system/
├── admin/                  # Admin-side files
├── assets/                 # CSS, JS, and image files
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # Database configuration
├── includes/               # Reusable PHP components
├── pages/                  # Client-side pages
├── uploads/                # Uploaded images
└── index.php               # Main entry point
```

## Usage

### Client-Side
1. Browse the menu and add items to cart
2. Place orders after logging in
3. Make table reservations
4. View order and reservation history in profile

### Admin-Side
1. Log in to the admin panel at:
   ```
   http://localhost/restaurant-management-system3/admin/
   ```
2. Manage menu items, orders, reservations, tables, and users
3. View sales and reservation statistics on the dashboard

## Adding Sample Data

To add sample menu items:
1. Log in to the admin panel
2. Go to Menu Management
3. Click "Add New Item" and fill in the details
4. Upload an image for the menu item

## Customization

### Site Settings
You can customize the site name, URL, and admin email in:
```
config/config.php
```

### Styling
Modify the CSS files in:
```
assets/css/
```

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements
- Bootstrap for the responsive design
- Chart.js for the admin dashboard charts
- Font Awesome for the icons
