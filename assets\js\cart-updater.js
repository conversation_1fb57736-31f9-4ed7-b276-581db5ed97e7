/**
 * Cart Updater - Direct DOM manipulation to update cart count
 * This script ensures the cart count updates immediately without page refresh
 */

// Function to update all cart badges on the page
function updateCartBadges(count) {
    // Find all cart icon containers
    const cartIconContainers = document.querySelectorAll('.cart-icon-container');

    // Update each container's badge
    cartIconContainers.forEach(container => {
        // Find existing badge
        let badge = container.querySelector('.badge');

        // If badge doesn't exist, create a new one
        if (!badge) {
            badge = document.createElement('span');
            badge.className = 'badge bg-danger cart-badge';
            container.appendChild(badge);
        }

        // Update badge text and ensure it's visible
        badge.textContent = count;
        badge.style.display = 'flex';
    });

    // For backward compatibility, also check for cart icons without containers
    const standaloneCartIcons = document.querySelectorAll('.fa-shopping-cart:not(.cart-icon-container .fa-shopping-cart)');

    standaloneCartIcons.forEach(icon => {
        // Check if parent is already a container
        if (!icon.parentNode.classList.contains('cart-icon-container')) {
            // Create container around the icon
            const container = document.createElement('span');
            container.className = 'cart-icon-container';

            // Replace icon with container + icon
            const parent = icon.parentNode;
            parent.insertBefore(container, icon);
            container.appendChild(icon);

            // Create badge
            const badge = document.createElement('span');
            badge.className = 'badge bg-danger cart-badge';
            badge.textContent = count;
            badge.style.display = 'flex';
            container.appendChild(badge);
        }
    });
}

// Function to get current cart count
function getCurrentCartCount() {
    const badge = document.querySelector('.cart-badge');
    return badge ? parseInt(badge.textContent) || 0 : 0;
}

// Function to increment cart count by a specific quantity
function incrementCartCount(quantity) {
    const currentCount = getCurrentCartCount();
    const newCount = currentCount + parseInt(quantity);
    updateCartBadges(newCount);
    return newCount;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all "Add to Cart" buttons
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function() {
            // Get the quantity from the form
            const form = document.getElementById('add-to-cart-form-' + this.getAttribute('data-id'));
            const quantity = parseInt(form.querySelector('input[name="quantity"]').value) || 1;

            // Increment the cart count immediately for instant feedback
            incrementCartCount(quantity);
        });
    });
});
