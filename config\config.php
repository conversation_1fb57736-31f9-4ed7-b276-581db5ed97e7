<?php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'raymart_diner');

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
if ($conn->query($sql) === FALSE) {
    die("Error creating database: " . $conn->error);
}

$conn->select_db(DB_NAME);

$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating users table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS admin (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating admin table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS menu_items (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    image VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating menu_items table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS orders (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    items TEXT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') DEFAULT 'cash',
    status ENUM('pending', 'preparing', 'completed', 'cancelled') DEFAULT 'pending',
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating orders table: " . $conn->error);
}

$result = $conn->query("SHOW COLUMNS FROM orders LIKE 'payment_method'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE orders ADD COLUMN payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') DEFAULT 'cash' AFTER total_price";
    $conn->query($sql);
}

$sql = "CREATE TABLE IF NOT EXISTS reservations (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    date DATE NOT NULL,
    time TIME NOT NULL,
    guests INT(11) NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating reservations table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS tables (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    capacity INT(11) NOT NULL,
    is_reserved BOOLEAN DEFAULT FALSE
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating tables table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS receipts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    file_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating receipts table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS email_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    type ENUM('order', 'reservation', 'admin_notification') NOT NULL,
    status ENUM('sent', 'failed') NOT NULL,
    recipient VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating email_logs table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') NOT NULL,
    transaction_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    transaction_reference VARCHAR(100),
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating payment_transactions table: " . $conn->error);
}



$sql = "CREATE TABLE IF NOT EXISTS order_items (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    menu_item_id INT(11) NOT NULL,
    quantity INT(11) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE RESTRICT
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating order_items table: " . $conn->error);
}

$sql = "CREATE TABLE IF NOT EXISTS reservation_tables (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    reservation_id INT(11) NOT NULL,
    table_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE CASCADE,
    UNIQUE KEY unique_reservation_table (reservation_id, table_id)
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating reservation_tables table: " . $conn->error);
}

$result = $conn->query("SHOW COLUMNS FROM reservations LIKE 'duration'");
if ($result->num_rows > 0) {
    $sql = "ALTER TABLE reservations DROP COLUMN duration";
    $conn->query($sql);
}

$result = $conn->query("SHOW COLUMNS FROM email_logs LIKE 'recipient'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE email_logs ADD COLUMN recipient VARCHAR(100) NOT NULL AFTER status";
    $conn->query($sql);
}

$result = $conn->query("SHOW COLUMNS FROM email_logs LIKE 'subject'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE email_logs ADD COLUMN subject VARCHAR(255) NOT NULL AFTER recipient";
    $conn->query($sql);
}

$sql = "ALTER TABLE email_logs MODIFY COLUMN type ENUM('order', 'reservation', 'admin_notification') NOT NULL";
$conn->query($sql);

// ===== DATABASE NORMALIZATION (3NF+) =====
// Create reference tables for full normalization

// 1. Categories table for menu items
$sql = "CREATE TABLE IF NOT EXISTS categories (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    display_order INT(11) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating categories table: " . $conn->error);
}

// 2. Payment methods reference table
$sql = "CREATE TABLE IF NOT EXISTS payment_methods (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT(11) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating payment_methods table: " . $conn->error);
}

// 3. Order statuses reference table
$sql = "CREATE TABLE IF NOT EXISTS order_statuses (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT(11) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating order_statuses table: " . $conn->error);
}

// 4. Reservation statuses reference table
$sql = "CREATE TABLE IF NOT EXISTS reservation_statuses (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT(11) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating reservation_statuses table: " . $conn->error);
}

// 5. Email types reference table
$sql = "CREATE TABLE IF NOT EXISTS email_types (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(30) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating email_types table: " . $conn->error);
}

// 6. Transaction statuses reference table
$sql = "CREATE TABLE IF NOT EXISTS transaction_statuses (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT(11) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
if ($conn->query($sql) === FALSE) {
    die("Error creating transaction_statuses table: " . $conn->error);
}

// ===== POPULATE REFERENCE TABLES =====

// Populate categories from existing menu items
$result = $conn->query("SELECT COUNT(*) as count FROM categories");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    // Get distinct categories from menu_items
    $result = $conn->query("SELECT DISTINCT category FROM menu_items WHERE category IS NOT NULL AND category != ''");
    $order = 1;
    while ($row = $result->fetch_assoc()) {
        $category = $row['category'];
        $stmt = $conn->prepare("INSERT INTO categories (name, display_order) VALUES (?, ?)");
        $stmt->bind_param("si", $category, $order);
        $stmt->execute();
        $order++;
    }
}

// Populate payment methods
$result = $conn->query("SELECT COUNT(*) as count FROM payment_methods");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    $payment_methods = [
        ['cash', 'Cash', 'Cash payment', 1],
        ['credit_card', 'Credit Card', 'Credit card payment', 2],
        ['debit_card', 'Debit Card', 'Debit card payment', 3],
        ['gcash', 'GCash', 'GCash digital payment', 4],
        ['paymaya', 'PayMaya', 'PayMaya digital payment', 5]
    ];

    $stmt = $conn->prepare("INSERT INTO payment_methods (code, name, description, display_order) VALUES (?, ?, ?, ?)");
    foreach ($payment_methods as $method) {
        $stmt->bind_param("sssi", $method[0], $method[1], $method[2], $method[3]);
        $stmt->execute();
    }
}

// Populate order statuses
$result = $conn->query("SELECT COUNT(*) as count FROM order_statuses");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    $order_statuses = [
        ['pending', 'Pending', 'Order is pending confirmation', 1],
        ['preparing', 'Preparing', 'Order is being prepared', 2],
        ['completed', 'Completed', 'Order has been completed', 3],
        ['cancelled', 'Cancelled', 'Order has been cancelled', 4]
    ];

    $stmt = $conn->prepare("INSERT INTO order_statuses (code, name, description, display_order) VALUES (?, ?, ?, ?)");
    foreach ($order_statuses as $status) {
        $stmt->bind_param("sssi", $status[0], $status[1], $status[2], $status[3]);
        $stmt->execute();
    }
}

// Populate reservation statuses
$result = $conn->query("SELECT COUNT(*) as count FROM reservation_statuses");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    $reservation_statuses = [
        ['pending', 'Pending', 'Reservation is pending confirmation', 1],
        ['confirmed', 'Confirmed', 'Reservation has been confirmed', 2],
        ['cancelled', 'Cancelled', 'Reservation has been cancelled', 3]
    ];

    $stmt = $conn->prepare("INSERT INTO reservation_statuses (code, name, description, display_order) VALUES (?, ?, ?, ?)");
    foreach ($reservation_statuses as $status) {
        $stmt->bind_param("sssi", $status[0], $status[1], $status[2], $status[3]);
        $stmt->execute();
    }
}

// Populate email types
$result = $conn->query("SELECT COUNT(*) as count FROM email_types");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    $email_types = [
        ['order', 'Order Notification', 'Email notifications related to orders'],
        ['reservation', 'Reservation Notification', 'Email notifications related to reservations'],
        ['admin_notification', 'Admin Notification', 'Email notifications sent to administrators']
    ];

    $stmt = $conn->prepare("INSERT INTO email_types (code, name, description) VALUES (?, ?, ?)");
    foreach ($email_types as $type) {
        $stmt->bind_param("sss", $type[0], $type[1], $type[2]);
        $stmt->execute();
    }
}

// Populate transaction statuses
$result = $conn->query("SELECT COUNT(*) as count FROM transaction_statuses");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    $transaction_statuses = [
        ['pending', 'Pending', 'Transaction is pending', 1],
        ['completed', 'Completed', 'Transaction has been completed', 2],
        ['failed', 'Failed', 'Transaction has failed', 3]
    ];

    $stmt = $conn->prepare("INSERT INTO transaction_statuses (code, name, description, display_order) VALUES (?, ?, ?, ?)");
    foreach ($transaction_statuses as $status) {
        $stmt->bind_param("sssi", $status[0], $status[1], $status[2], $status[3]);
        $stmt->execute();
    }
}

$admin_username = 'admin';
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$admin_name = 'Administrator';
$admin_email = '<EMAIL>';

$stmt = $conn->prepare("SELECT id FROM admin WHERE username = ?");
$stmt->bind_param("s", $admin_username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $stmt = $conn->prepare("INSERT INTO admin (username, password, name, email) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $admin_username, $admin_password, $admin_name, $admin_email);
    $stmt->execute();
}

$stmt = $conn->prepare("SELECT id FROM tables LIMIT 1");
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    for ($i = 1; $i <= 10; $i++) {
        $capacity = ($i <= 5) ? 2 : (($i <= 8) ? 4 : 6);
        $stmt = $conn->prepare("INSERT INTO tables (capacity, is_reserved) VALUES (?, FALSE)");
        $stmt->bind_param("i", $capacity);
        $stmt->execute();
    }
}

// ===== ADD FOREIGN KEY COLUMNS =====

// Add category_id to menu_items table
$result = $conn->query("SHOW COLUMNS FROM menu_items LIKE 'category_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE menu_items ADD COLUMN category_id INT(11) AFTER category";
    $conn->query($sql);
}

// Add payment_method_id to orders table
$result = $conn->query("SHOW COLUMNS FROM orders LIKE 'payment_method_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE orders ADD COLUMN payment_method_id INT(11) AFTER payment_method";
    $conn->query($sql);
}

// Add status_id to orders table
$result = $conn->query("SHOW COLUMNS FROM orders LIKE 'status_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE orders ADD COLUMN status_id INT(11) AFTER status";
    $conn->query($sql);
}

// Add status_id to reservations table
$result = $conn->query("SHOW COLUMNS FROM reservations LIKE 'status_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE reservations ADD COLUMN status_id INT(11) AFTER status";
    $conn->query($sql);
}

// Add type_id to email_logs table
$result = $conn->query("SHOW COLUMNS FROM email_logs LIKE 'type_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE email_logs ADD COLUMN type_id INT(11) AFTER type";
    $conn->query($sql);
}

// Add payment_method_id to payment_transactions table
$result = $conn->query("SHOW COLUMNS FROM payment_transactions LIKE 'payment_method_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE payment_transactions ADD COLUMN payment_method_id INT(11) AFTER payment_method";
    $conn->query($sql);
}

// Add transaction_status_id to payment_transactions table
$result = $conn->query("SHOW COLUMNS FROM payment_transactions LIKE 'transaction_status_id'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE payment_transactions ADD COLUMN transaction_status_id INT(11) AFTER transaction_status";
    $conn->query($sql);
}

// ===== MIGRATE EXISTING DATA =====

// Migrate menu_items categories
$conn->query("UPDATE menu_items mi
              JOIN categories c ON mi.category = c.name
              SET mi.category_id = c.id
              WHERE mi.category_id IS NULL");

// Migrate orders payment methods
$conn->query("UPDATE orders o
              JOIN payment_methods pm ON o.payment_method = pm.code
              SET o.payment_method_id = pm.id
              WHERE o.payment_method_id IS NULL");

// Migrate orders statuses
$conn->query("UPDATE orders o
              JOIN order_statuses os ON o.status = os.code
              SET o.status_id = os.id
              WHERE o.status_id IS NULL");

// Migrate reservations statuses
$conn->query("UPDATE reservations r
              JOIN reservation_statuses rs ON r.status = rs.code
              SET r.status_id = rs.id
              WHERE r.status_id IS NULL");

// Migrate email_logs types
$conn->query("UPDATE email_logs el
              JOIN email_types et ON el.type = et.code
              SET el.type_id = et.id
              WHERE el.type_id IS NULL");

// Migrate payment_transactions payment methods
$conn->query("UPDATE payment_transactions pt
              JOIN payment_methods pm ON pt.payment_method = pm.code
              SET pt.payment_method_id = pm.id
              WHERE pt.payment_method_id IS NULL");

// Migrate payment_transactions statuses
$conn->query("UPDATE payment_transactions pt
              JOIN transaction_statuses ts ON pt.transaction_status = ts.code
              SET pt.transaction_status_id = ts.id
              WHERE pt.transaction_status_id IS NULL");

// ===== ADD FOREIGN KEY CONSTRAINTS =====

// Add foreign key constraints after data migration
$constraints = [
    "ALTER TABLE menu_items ADD CONSTRAINT fk_menu_items_category
     FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT",

    "ALTER TABLE orders ADD CONSTRAINT fk_orders_payment_method
     FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id) ON DELETE RESTRICT",

    "ALTER TABLE orders ADD CONSTRAINT fk_orders_status
     FOREIGN KEY (status_id) REFERENCES order_statuses(id) ON DELETE RESTRICT",

    "ALTER TABLE reservations ADD CONSTRAINT fk_reservations_status
     FOREIGN KEY (status_id) REFERENCES reservation_statuses(id) ON DELETE RESTRICT",

    "ALTER TABLE email_logs ADD CONSTRAINT fk_email_logs_type
     FOREIGN KEY (type_id) REFERENCES email_types(id) ON DELETE RESTRICT",

    "ALTER TABLE payment_transactions ADD CONSTRAINT fk_payment_transactions_payment_method
     FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id) ON DELETE RESTRICT",

    "ALTER TABLE payment_transactions ADD CONSTRAINT fk_payment_transactions_status
     FOREIGN KEY (transaction_status_id) REFERENCES transaction_statuses(id) ON DELETE RESTRICT"
];

foreach ($constraints as $constraint) {
    // Check if constraint already exists before adding
    $constraint_name = '';
    if (preg_match('/CONSTRAINT\s+(\w+)/', $constraint, $matches)) {
        $constraint_name = $matches[1];

        // Check if constraint exists
        $check_sql = "SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                      WHERE CONSTRAINT_SCHEMA = DATABASE() AND CONSTRAINT_NAME = ?";
        $stmt = $conn->prepare($check_sql);
        $stmt->bind_param("s", $constraint_name);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            $conn->query($constraint);
        }
    }
}

// ===== ADD INDEXES FOR PERFORMANCE =====

$indexes = [
    "CREATE INDEX IF NOT EXISTS idx_menu_items_category_id ON menu_items(category_id)",
    "CREATE INDEX IF NOT EXISTS idx_orders_payment_method_id ON orders(payment_method_id)",
    "CREATE INDEX IF NOT EXISTS idx_orders_status_id ON orders(status_id)",
    "CREATE INDEX IF NOT EXISTS idx_reservations_status_id ON reservations(status_id)",
    "CREATE INDEX IF NOT EXISTS idx_email_logs_type_id ON email_logs(type_id)",
    "CREATE INDEX IF NOT EXISTS idx_payment_transactions_payment_method_id ON payment_transactions(payment_method_id)",
    "CREATE INDEX IF NOT EXISTS idx_payment_transactions_status_id ON payment_transactions(transaction_status_id)",
    "CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)",
    "CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active)",
    "CREATE INDEX IF NOT EXISTS idx_payment_methods_code ON payment_methods(code)",
    "CREATE INDEX IF NOT EXISTS idx_payment_methods_active ON payment_methods(is_active)",
    "CREATE INDEX IF NOT EXISTS idx_order_statuses_code ON order_statuses(code)",
    "CREATE INDEX IF NOT EXISTS idx_reservation_statuses_code ON reservation_statuses(code)",
    "CREATE INDEX IF NOT EXISTS idx_email_types_code ON email_types(code)",
    "CREATE INDEX IF NOT EXISTS idx_transaction_statuses_code ON transaction_statuses(code)"
];

foreach ($indexes as $index) {
    $conn->query($index);
}

define('SITE_NAME', 'RAYMART\'S DINER');
define('SITE_URL', 'http://localhost/restaurant-management-system3');
define('ADMIN_EMAIL', '<EMAIL>');
?>
