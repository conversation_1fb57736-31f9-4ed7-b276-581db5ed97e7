<?php
session_start();
include '../config/config.php';
include '../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Handle reservation actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {


    // Update reservation status
    if (isset($_POST['update_status'])) {
        $reservation_id = (int)$_POST['reservation_id'];
        $status = sanitize($_POST['status']);

        $stmt = $conn->prepare("UPDATE reservations SET status = ? WHERE id = ?");
        $stmt->bind_param("si", $status, $reservation_id);

        if ($stmt->execute()) {
            // Get reservation details
            $stmt = $conn->prepare("SELECT * FROM reservations WHERE id = ?");
            $stmt->bind_param("i", $reservation_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $reservation = $result->fetch_assoc();

            // Get user details
            $user = getUserById($reservation['user_id']);

            // Send email notification
            if ($user) {
                $subject = 'Reservation Status Update - ' . SITE_NAME;
                $message = "Dear " . $user['name'] . ",\n\n";
                $message .= "The status of your reservation #" . $reservation_id . " has been updated to " . ucfirst($status) . ".\n\n";
                $message .= "Reservation Details:\n";
                $message .= "Date: " . formatDate($reservation['date']) . "\n";
                $message .= "Time: " . formatTime($reservation['time']) . "\n";
                $message .= "Number of Guests: " . $reservation['guests'] . "\n\n";

                if ($status == 'confirmed') {
                    $message .= "Your reservation has been confirmed. We look forward to serving you!\n\n";
                } else if ($status == 'cancelled') {
                    $message .= "Your reservation has been cancelled. We hope to see you another time.\n\n";
                }

                $message .= "Thank you for choosing " . SITE_NAME . "!\n";

                sendEmail($user['email'], $subject, $message, $user['id'], 'reservation');
            }

            // Set success message
            $_SESSION['message'] = 'Reservation status updated successfully!';
            $_SESSION['message_type'] = 'success';
        } else {
            // Set error message
            $_SESSION['message'] = 'Failed to update reservation status. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh page
    header("Location: reservations.php");
    exit();
}

// Handle table assignment
if (isset($_POST['assign_tables'])) {
    $reservation_id = (int)$_POST['reservation_id'];
    $table_ids = isset($_POST['table_ids']) ? $_POST['table_ids'] : [];

    if (empty($table_ids)) {
        $_SESSION['message'] = 'Please select at least one table to assign.';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Assign tables to reservation
        if (assignTablesToReservation($reservation_id, $table_ids)) {
            $_SESSION['message'] = 'Tables assigned successfully!';
            $_SESSION['message_type'] = 'success';

            // Get reservation details
            $stmt = $conn->prepare("SELECT * FROM reservations WHERE id = ?");
            $stmt->bind_param("i", $reservation_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $reservation = $result->fetch_assoc();

            // Get user details
            $user = getUserById($reservation['user_id']);

            // Get assigned tables for email
            $assigned_tables = getTablesForReservation($reservation_id);
            $table_info = '';
            foreach ($assigned_tables as $index => $table) {
                $table_info .= "Table #" . $table['id'] . " (Capacity: " . $table['capacity'] . ")";
                if ($index < count($assigned_tables) - 1) {
                    $table_info .= ", ";
                }
            }

            // Send email notification
            if ($user) {
                $subject = 'Table Assignment Update - ' . SITE_NAME;
                $message = "Dear " . $user['name'] . ",\n\n";
                $message .= "We have assigned " . (count($assigned_tables) > 1 ? "tables" : "a table") . " for your reservation #" . $reservation_id . ".\n\n";
                $message .= "Reservation Details:\n";
                $message .= "Date: " . formatDate($reservation['date']) . "\n";
                $message .= "Time: " . formatTime($reservation['time']) . "\n";
                $message .= "Number of Guests: " . $reservation['guests'] . "\n";
                $message .= "Assigned " . (count($assigned_tables) > 1 ? "Tables" : "Table") . ": " . $table_info . "\n\n";
                $message .= "We look forward to serving you!\n\n";
                $message .= "Thank you for choosing " . SITE_NAME . "!\n";

                sendEmail($user['email'], $subject, $message, $user['id'], 'reservation');
            }
        } else {
            $_SESSION['message'] = 'Failed to assign tables. Please try again.';
            $_SESSION['message_type'] = 'danger';
        }
    }

    // Redirect to refresh page
    header("Location: reservations.php");
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$date_filter = isset($_GET['date']) ? sanitize($_GET['date']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$query = "SELECT r.*, u.name as user_name, u.email as user_email, u.phone as user_phone FROM reservations r LEFT JOIN users u ON r.user_id = u.id WHERE 1=1";
$params = [];
$types = "";

if (!empty($status_filter)) {
    $query .= " AND r.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($date_filter)) {
    $query .= " AND r.date = ?";
    $params[] = $date_filter;
    $types .= "s";
}

if (!empty($search)) {
    $search_term = "%$search%";
    $query .= " AND (u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= "sss";
}

$query .= " ORDER BY r.date DESC, r.time DESC";

// Prepare and execute query
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

$reservations = [];
while ($row = $result->fetch_assoc()) {
    // Get assigned tables for each reservation
    $row['tables'] = getTablesForReservation($row['id']);
    $reservations[] = $row;
}

// Get all tables for assignment
$stmt = $conn->prepare("SELECT * FROM tables ORDER BY capacity ASC");
$stmt->execute();
$result = $stmt->get_result();
$all_tables = [];
while ($row = $result->fetch_assoc()) {
    $all_tables[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservation Management - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin.css">

    <!-- Custom CSS for Buttons -->
    <style>
        .btn-dark {
            background-color: #212529;
            border-color: #212529;
            box-shadow: none;
        }

        .btn-dark:hover,
        .btn-dark:focus,
        .btn-dark:active {
            background-color: #1a1e21;
            border-color: #1a1e21;
            box-shadow: none !important;
        }

        .action-button {
            width: 180px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 60px;
            border-radius: 4px;
            font-weight: normal;
            box-shadow: none;
            padding: 8px 15px;
            margin-bottom: 8px;
        }

        .action-button i {
            margin-bottom: 5px;
            font-size: 1.2rem;
            text-align: center;
        }

        .action-button .button-text {
            display: block;
            line-height: 1.2;
            width: 100%;
            text-align: center;
        }

        .action-button .button-text span {
            display: inline-block;
            font-size: 0.9rem;
            white-space: nowrap;
            font-weight: normal;
            text-align: center;
        }

        /* Specific button styles to match the image */
        .btn-dark.action-button {
            background-color: #212529;
            border-color: #212529;
        }

        .btn-info.action-button {
            background-color: #00c3e3;
            border-color: #00c3e3;
        }

        .btn-success.action-button {
            background-color: #1E8D5C;
            border-color: #1E8D5C;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>RAYMA<span style="color: #cc1500;">R</span>T'S DINER</h3>
            <p>Admin Panel</p>
        </div>

        <div class="sidebar-menu">
            <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="menu.php"><i class="fas fa-utensils"></i> Menu Management</a>
            <a href="orders.php"><i class="fas fa-shopping-cart"></i> Order Management</a>
            <a href="payments.php"><i class="fas fa-money-bill-wave"></i> Payment Management</a>
            <a href="reservations.php" class="active"><i class="fas fa-calendar-alt"></i> Reservations</a>
            <a href="tables.php"><i class="fas fa-chair"></i> Table Management</a>
            <a href="users.php"><i class="fas fa-users"></i> User Management</a>
            <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="container-fluid">
            <h1 class="mb-4">Reservation Management</h1>

            <?php if (isset($_SESSION['message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="filter-card">
                <form method="get" action="reservations.php" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="confirmed" <?php echo $status_filter == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                            <option value="cancelled" <?php echo $status_filter == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                    </div>

                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Customer Name, Email, Phone" value="<?php echo $search; ?>">
                    </div>

                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">Filter</button>
                        <a href="reservations.php" class="btn btn-secondary">Reset</a>
                    </div>
                </form>
            </div>

            <!-- Reservations Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 5%;">ID</th>
                                    <th style="width: 15%;">Customer</th>
                                    <th style="width: 10%;">Date</th>
                                    <th style="width: 8%;">Time</th>
                                    <th style="width: 5%;">Guests</th>
                                    <th style="width: 8%;">Status</th>
                                    <th style="width: 12%;">Tables</th>
                                    <th style="width: 37%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($reservations)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No reservations found.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($reservations as $reservation): ?>
                                        <tr>
                                            <td>#<?php echo $reservation['id']; ?></td>
                                            <td>
                                                <?php echo $reservation['user_name']; ?><br>
                                                <small class="text-muted"><?php echo $reservation['user_email']; ?></small><br>
                                                <small class="text-muted"><?php echo $reservation['user_phone'] ? $reservation['user_phone'] : 'No phone'; ?></small>
                                            </td>
                                            <td><?php echo formatDate($reservation['date']); ?></td>
                                            <td><?php echo formatTime($reservation['time']); ?></td>
                                            <td><?php echo $reservation['guests']; ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch ($reservation['status']) {
                                                    case 'pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'confirmed':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($reservation['status']); ?></span>
                                            </td>
                                            <td>
                                                <?php if (!empty($reservation['tables'])): ?>
                                                    <?php foreach ($reservation['tables'] as $index => $table): ?>
                                                        <span class="badge bg-info">Table #<?php echo $table['id']; ?> (<?php echo $table['capacity']; ?>)</span>
                                                        <?php if ($index < count($reservation['tables']) - 1): ?><br><?php endif; ?>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">No tables assigned</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column align-items-center">
                                                    <button type="button" class="btn btn-dark action-button" data-bs-toggle="modal" data-bs-target="#updateStatusModal" data-reservation-id="<?php echo $reservation['id']; ?>" data-status="<?php echo $reservation['status']; ?>">
                                                        <i class="fas fa-edit"></i>
                                                        <div class="button-text">
                                                            <span>Update Status</span>
                                                        </div>
                                                    </button>
                                                    <button type="button" class="btn btn-info action-button" data-bs-toggle="modal" data-bs-target="#assignTablesModal"
                                                        data-reservation-id="<?php echo $reservation['id']; ?>"
                                                        data-guests="<?php echo $reservation['guests']; ?>"
                                                        data-date="<?php echo $reservation['date']; ?>"
                                                        data-time="<?php echo $reservation['time']; ?>">
                                                        <i class="fas fa-chair"></i>
                                                        <div class="button-text">
                                                            <span>Manage Tables</span>
                                                        </div>
                                                    </button>

                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Update Reservation Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="reservations.php">
                    <div class="modal-body">
                        <input type="hidden" id="update_reservation_id" name="reservation_id">

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="update_status" name="status" required>
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-dark" name="update_status">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Assign Tables Modal -->
    <div class="modal fade" id="assignTablesModal" tabindex="-1" aria-labelledby="assignTablesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignTablesModalLabel">Assign Tables to Reservation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="reservations.php">
                    <div class="modal-body">
                        <input type="hidden" id="assign_reservation_id" name="reservation_id">

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Date:</label>
                                    <div id="reservation_date" class="form-control-static"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Time:</label>
                                    <div id="reservation_time" class="form-control-static"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Guests:</label>
                                    <div id="reservation_guests" class="form-control-static"></div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Available Tables:</label>
                            <div id="available_tables_loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p>Loading available tables...</p>
                            </div>
                            <div id="available_tables_container" class="row" style="display: none;">
                                <?php foreach ($all_tables as $table): ?>
                                <div class="col-md-3 mb-2 table-option" data-table-id="<?php echo $table['id']; ?>" data-capacity="<?php echo $table['capacity']; ?>">
                                    <div class="card">
                                        <div class="card-body p-2 text-center">
                                            <div class="form-check">
                                                <input class="form-check-input table-checkbox" type="checkbox" name="table_ids[]" value="<?php echo $table['id']; ?>" id="table_<?php echo $table['id']; ?>">
                                                <label class="form-check-label w-100" for="table_<?php echo $table['id']; ?>">
                                                    Table #<?php echo $table['id']; ?><br>
                                                    <small>Capacity: <?php echo $table['capacity']; ?></small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div id="no_tables_message" class="alert alert-warning" style="display: none;">
                                No tables are available for this reservation's date and time.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Currently Assigned Tables:</label>
                            <div id="assigned_tables"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" name="assign_tables">Assign Tables</button>
                    </div>
                </form>
            </div>
        </div>
    </div>



    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Update Status Modal
        const updateStatusModal = document.getElementById('updateStatusModal');
        updateStatusModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const reservationId = button.getAttribute('data-reservation-id');
            const status = button.getAttribute('data-status');

            document.getElementById('update_reservation_id').value = reservationId;
            document.getElementById('update_status').value = status;
        });

        // Assign Tables Modal
        const assignTablesModal = document.getElementById('assignTablesModal');
        assignTablesModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const reservationId = button.getAttribute('data-reservation-id');
            const guests = button.getAttribute('data-guests');
            const date = button.getAttribute('data-date');
            const time = button.getAttribute('data-time');

            // Set reservation details
            document.getElementById('assign_reservation_id').value = reservationId;
            document.getElementById('reservation_date').textContent = formatDate(date);
            document.getElementById('reservation_time').textContent = formatTime(time);
            document.getElementById('reservation_guests').textContent = guests;

            // Reset checkboxes
            document.querySelectorAll('.table-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.disabled = false;
            });

            // Show loading indicator
            document.getElementById('available_tables_loading').style.display = 'block';
            document.getElementById('available_tables_container').style.display = 'none';
            document.getElementById('no_tables_message').style.display = 'none';

            // Get assigned tables
            fetch(`<?php echo SITE_URL; ?>/admin/get_assigned_tables.php?reservation_id=${reservationId}`)
                .then(response => response.json())
                .then(data => {
                    const assignedTablesContainer = document.getElementById('assigned_tables');
                    assignedTablesContainer.innerHTML = '';

                    if (data.tables && data.tables.length > 0) {
                        const tablesList = document.createElement('ul');
                        tablesList.className = 'list-group';

                        data.tables.forEach(table => {
                            const listItem = document.createElement('li');
                            listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                            listItem.innerHTML = `Table #${table.id} <span class="badge bg-primary rounded-pill">Capacity: ${table.capacity}</span>`;
                            tablesList.appendChild(listItem);

                            // Check the corresponding checkbox
                            const checkbox = document.getElementById(`table_${table.id}`);
                            if (checkbox) {
                                checkbox.checked = true;
                            }
                        });

                        assignedTablesContainer.appendChild(tablesList);
                    } else {
                        assignedTablesContainer.innerHTML = '<div class="alert alert-info">No tables currently assigned to this reservation.</div>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching assigned tables:', error);
                    document.getElementById('assigned_tables').innerHTML = '<div class="alert alert-danger">Error loading assigned tables.</div>';
                });

            // Get available tables
            fetch(`<?php echo SITE_URL; ?>/admin/get_available_tables.php?reservation_id=${reservationId}&date=${date}&time=${time}&guests=${guests}`)
                .then(response => response.json())
                .then(data => {
                    // Hide loading indicator
                    document.getElementById('available_tables_loading').style.display = 'none';

                    if (data.available_tables && data.available_tables.length > 0) {
                        document.getElementById('available_tables_container').style.display = 'flex';

                        // Disable tables that are not available
                        document.querySelectorAll('.table-option').forEach(tableOption => {
                            const tableId = parseInt(tableOption.getAttribute('data-table-id'));
                            const isAvailable = data.available_tables.some(table => table.id === tableId);

                            if (!isAvailable) {
                                const checkbox = tableOption.querySelector('.table-checkbox');
                                if (checkbox && !checkbox.checked) {
                                    checkbox.disabled = true;
                                    tableOption.classList.add('opacity-50');
                                }
                            } else {
                                tableOption.classList.remove('opacity-50');
                            }
                        });
                    } else {
                        document.getElementById('no_tables_message').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error fetching available tables:', error);
                    document.getElementById('available_tables_loading').style.display = 'none';
                    document.getElementById('no_tables_message').style.display = 'block';
                    document.getElementById('no_tables_message').textContent = 'Error loading available tables.';
                });
        });

        // Helper functions for formatting date and time
        function formatDate(dateString) {
            const date = new Date(dateString);
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            return date.toLocaleDateString('en-US', options);
        }

        function formatTime(timeString) {
            const [hours, minutes] = timeString.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12;
            return `${hour12}:${minutes} ${ampm}`;
        }


    </script>
</body>
</html>
