<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - <?php echo ucfirst($page); ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">

    <!-- Favicon -->
    <link rel="icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico" type="image/x-icon">

    <!-- Cart Updater Script - Direct DOM manipulation -->
    <script src="<?php echo SITE_URL; ?>/assets/js/cart-updater.js"></script>

    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

    <!-- jQuery UI for Datepicker -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

    <!-- jQuery Timepicker (better version) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-timepicker/1.14.0/jquery.timepicker.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-timepicker/1.14.0/jquery.timepicker.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>" style="pointer-events: auto; text-decoration: none !important;">
                RAYMA<span style="color: #cc1500;">R</span>T'S DINER
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Left side empty -->
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($page == 'home') ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($page == 'menu') ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/index.php?page=menu">Menu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($page == 'reservation') ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/index.php?page=reservation">Reservations</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($page == 'cart') ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/index.php?page=cart">
                            <span class="cart-icon-container">
                                <i class="fas fa-shopping-cart"></i>
                                <?php if (isset($_SESSION['cart']) && count($_SESSION['cart']) > 0): ?>
                                    <?php
                                        // Calculate total items in cart
                                        $total_items = 0;
                                        foreach ($_SESSION['cart'] as $item) {
                                            $total_items += $item['quantity'];
                                        }
                                    ?>
                                    <span class="badge bg-danger cart-badge"><?php echo $total_items; ?></span>
                                <?php endif; ?>
                            </span>
                            Cart
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> <?php echo getUserById($_SESSION['user_id'])['name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/index.php?page=profile">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/index.php?page=logout">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo ($page == 'login' || $page == 'register') ? 'active' : ''; ?>" href="#" id="loginDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> Login
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="loginDropdown">
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/index.php?page=login">Login</a></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/index.php?page=register">Register</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/login.php" target="_blank"><i class="fas fa-user-cog"></i> Admin</a></li>
                            </ul>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container my-4">
        <?php if (isset($_SESSION['message'])): ?>
            <?php if (strpos($_SESSION['message'], 'added to cart') !== false): ?>
                <!-- Cart-specific success message styled like the login notification -->
                <div class="alert alert-success alert-dismissible fade show" style="position: fixed; top: 70px; left: 50%; transform: translateX(-50%); z-index: 9999; width: 80%; max-width: 500px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <script>
                    // Auto-dismiss after 5 seconds
                    setTimeout(() => {
                        const notification = document.querySelector('.alert.alert-success');
                        if (notification) {
                            notification.remove();
                        }
                    }, 5000);
                </script>
            <?php else: ?>
                <!-- Regular message -->
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['cart_notification']) && (time() - $_SESSION['cart_notification']['timestamp']) < 5): ?>
            <!-- PHP Fallback Cart Alert Notification (Top) -->
            <div class="alert alert-success alert-dismissible fade show" style="position: fixed; top: 70px; left: 50%; transform: translateX(-50%); z-index: 9999; width: 80%; max-width: 500px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                <?php echo $_SESSION['cart_notification']['quantity']; ?>x <?php echo $_SESSION['cart_notification']['item_name']; ?> added to cart successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <script>
                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    const notification = document.querySelector('.alert.alert-success');
                    if (notification) {
                        notification.remove();
                    }
                }, 5000);
            </script>

            <!-- PHP Fallback Cart Notification (Side) -->
            <div class="cart-notification" style="position: fixed; top: 80px; right: 20px; z-index: 9999; background-color: #28a745; color: white; padding: 15px 20px; border-radius: 5px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); display: flex; align-items: center; max-width: 350px;">
                <i class="fas fa-check-circle" style="font-size: 24px; margin-right: 10px;"></i>
                <div class="cart-notification-content">
                    <h5 style="margin: 0 0 5px 0; font-size: 16px; font-weight: bold;">Nadagdag sa Cart!</h5>
                    <p style="margin: 0; font-size: 14px;"><?php echo $_SESSION['cart_notification']['quantity']; ?>x <?php echo $_SESSION['cart_notification']['item_name']; ?> ay nadagdag sa iyong cart</p>
                </div>
            </div>
            <script>
                // Remove the side notification after 5 seconds
                setTimeout(() => {
                    const sideNotification = document.querySelector('.cart-notification');
                    if (sideNotification) {
                        sideNotification.style.opacity = '0';
                        setTimeout(() => {
                            sideNotification.remove();
                        }, 500);
                    }
                }, 5000);
            </script>
            <?php unset($_SESSION['cart_notification']); ?>
        <?php endif; ?>
