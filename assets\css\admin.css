/* Admin Styles for RAYMART'S DINER */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Montserrat:wght@400;500;600;700&display=swap');

/* General Styles */
body {
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar */
.sidebar {
    min-height: 100vh;
    background-color: #212529;
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    z-index: 100;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    letter-spacing: 1px;
    color: #fff;
    margin-bottom: 15px;
    background-color: #212529; /* Dark charcoal */
    padding: 12px 18px;
    font-weight: 700;
    border-radius: 3px;
    pointer-events: none;
    position: relative;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu a {
    display: block;
    padding: 10px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Content */
.content {
    margin-left: 250px;
    padding: 20px;
}

/* Login Page */
.login-card {
    max-width: 400px;
    width: 100%;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: white;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo h1 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    letter-spacing: 1px;
    color: #fff;
    padding: 18px 30px;
    display: inline-block;
    background-color: #212529; /* Dark charcoal */
    margin-bottom: 20px;
    font-weight: 700;
    border-radius: 3px;
    pointer-events: none; /* Completely disable hover effects */
    position: relative; /* Required for absolute positioning of pseudo-element */
    background-image: none !important; /* Ensure no background image is used */
}

/* Cards */
.stat-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    text-align: center;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stat-card h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.stat-card p {
    color: #6c757d;
    margin-bottom: 0;
}

.chart-container {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.filter-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Tables */
.table-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.table-card:hover {
    transform: translateY(-5px);
}

.table-card.reserved {
    border-left: 5px solid #dc3545;
}

.table-card.available {
    border-left: 5px solid #28a745;
}

/* Buttons */
.btn-primary {
    background-color: #212529;
    border-color: #212529;
    box-shadow: 2px 2px 0 #800020;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #343a40;
    border-color: #212529;
    box-shadow: 3px 3px 0 #800020;
    transform: translate(-1px, -1px);
}

.btn-outline-primary {
    color: #212529;
    border-color: #212529;
    box-shadow: 1px 1px 0 #800020;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #212529;
    border-color: #212529;
    color: #fff;
    box-shadow: 2px 2px 0 #800020;
    transform: translate(-1px, -1px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .content {
        margin-left: 200px;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 0;
        overflow: hidden;
    }

    .content {
        margin-left: 0;
    }
}
