<?php
session_start();
include '../../config/config.php';
include '../../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get the requested chart type
$chart_type = isset($_GET['chart_type']) ? sanitize($_GET['chart_type']) : '';

// Initialize response array
$response = [];

// Get date range filters if provided
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';

// Build filters array
$filters = [];
if (!empty($date_from)) $filters['date_from'] = $date_from;
if (!empty($date_to)) $filters['date_to'] = $date_to;

switch ($chart_type) {
    case 'payment_method':
        $response = getPaymentMethodDistribution($filters);
        break;
    case 'transaction_status':
        $response = getTransactionStatusDistribution($filters);
        break;
    case 'monthly_volume':
        $response = getMonthlyTransactionVolume($filters);
        break;
    default:
        // Return all chart data
        $response = [
            'payment_method' => getPaymentMethodDistribution($filters),
            'transaction_status' => getTransactionStatusDistribution($filters),
            'monthly_volume' => getMonthlyTransactionVolume($filters)
        ];
        break;
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit();

/**
 * Get payment method distribution data
 *
 * @param array $filters Optional filters for the query
 * @return array Payment method distribution data
 */
function getPaymentMethodDistribution($filters = []) {
    global $conn;

    // Base query
    $query = "SELECT
                payment_method,
                COUNT(*) as count,
                SUM(amount) as total_amount
              FROM payment_transactions";

    // Add WHERE clauses if filters are provided
    $where_clauses = [];
    $params = [];
    $types = "";

    // Apply date filters
    if (!empty($filters['date_from'])) {
        $where_clauses[] = "transaction_date >= ?";
        $params[] = $filters['date_from'] . ' 00:00:00';
        $types .= "s";
    }

    if (!empty($filters['date_to'])) {
        $where_clauses[] = "transaction_date <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
        $types .= "s";
    }

    // Add WHERE clause to query if needed
    if (!empty($where_clauses)) {
        $query .= " WHERE " . implode(" AND ", $where_clauses);
    }

    // Group by payment method
    $query .= " GROUP BY payment_method";

    // Prepare and execute the query
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Initialize data arrays
    $labels = [];
    $data = [];
    $backgroundColor = [
        'cash' => 'rgba(75, 192, 192, 0.7)',
        'credit_card' => 'rgba(54, 162, 235, 0.7)',
        'debit_card' => 'rgba(153, 102, 255, 0.7)',
        'gcash' => 'rgba(255, 159, 64, 0.7)',
        'paymaya' => 'rgba(255, 99, 132, 0.7)'
    ];
    $colors = [];

    // Process results
    while ($row = $result->fetch_assoc()) {
        $method = ucwords(str_replace('_', ' ', $row['payment_method']));
        $labels[] = $method;
        $data[] = (int)$row['count'];
        $colors[] = $backgroundColor[$row['payment_method']] ?? 'rgba(128, 128, 128, 0.7)';
    }

    return [
        'labels' => $labels,
        'data' => $data,
        'backgroundColor' => $colors
    ];
}

/**
 * Get transaction status distribution data
 *
 * @param array $filters Optional filters for the query
 * @return array Transaction status distribution data
 */
function getTransactionStatusDistribution($filters = []) {
    global $conn;

    // Base query
    $query = "SELECT
                transaction_status,
                COUNT(*) as count
              FROM payment_transactions";

    // Add WHERE clauses if filters are provided
    $where_clauses = [];
    $params = [];
    $types = "";

    // Apply date filters
    if (!empty($filters['date_from'])) {
        $where_clauses[] = "transaction_date >= ?";
        $params[] = $filters['date_from'] . ' 00:00:00';
        $types .= "s";
    }

    if (!empty($filters['date_to'])) {
        $where_clauses[] = "transaction_date <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
        $types .= "s";
    }

    // Add WHERE clause to query if needed
    if (!empty($where_clauses)) {
        $query .= " WHERE " . implode(" AND ", $where_clauses);
    }

    // Group by transaction status
    $query .= " GROUP BY transaction_status";

    // Prepare and execute the query
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Initialize data arrays
    $labels = [];
    $data = [];
    $backgroundColor = [
        'pending' => 'rgba(255, 205, 86, 0.7)',
        'completed' => 'rgba(75, 192, 192, 0.7)',
        'failed' => 'rgba(255, 99, 132, 0.7)'
    ];
    $colors = [];

    // Process results
    while ($row = $result->fetch_assoc()) {
        $status = ucwords(str_replace('_', ' ', $row['transaction_status']));
        $labels[] = $status;
        $data[] = (int)$row['count'];
        $colors[] = $backgroundColor[$row['transaction_status']] ?? 'rgba(128, 128, 128, 0.7)';
    }

    return [
        'labels' => $labels,
        'data' => $data,
        'backgroundColor' => $colors
    ];
}

/**
 * Get monthly transaction volume data
 *
 * @param array $filters Optional filters for the query
 * @return array Monthly transaction volume data
 */
function getMonthlyTransactionVolume($filters = []) {
    global $conn;

    // Base query to get monthly transaction volume for the last 6 months
    $query = "SELECT
                DATE_FORMAT(transaction_date, '%Y-%m') as month,
                SUM(amount) as total_amount
              FROM payment_transactions
              WHERE transaction_status != 'failed'";

    // Add additional WHERE clauses if filters are provided
    $where_clauses = [];
    $params = [];
    $types = "";

    // Apply date filters
    if (!empty($filters['date_from'])) {
        $where_clauses[] = "transaction_date >= ?";
        $params[] = $filters['date_from'] . ' 00:00:00';
        $types .= "s";
    }

    if (!empty($filters['date_to'])) {
        $where_clauses[] = "transaction_date <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
        $types .= "s";
    }

    // Add WHERE clause to query if needed
    if (!empty($where_clauses)) {
        $query .= " AND " . implode(" AND ", $where_clauses);
    }

    // Group by month and order by month
    $query .= " GROUP BY DATE_FORMAT(transaction_date, '%Y-%m')
                ORDER BY month
                LIMIT 6";

    // Prepare and execute the query
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Initialize data arrays
    $labels = [];
    $data = [];

    // Process results
    while ($row = $result->fetch_assoc()) {
        $month_name = date('F Y', strtotime($row['month'] . '-01'));
        $labels[] = $month_name;
        $data[] = (float)$row['total_amount'];
    }

    return [
        'labels' => $labels,
        'data' => $data
    ];
}
