// Main JavaScript for Raymart's Diner

// PDF Generation Function
function generatePDF(orderId) {
    // Get order details from the modal
    const modalId = `orderModal${orderId}`;
    const modal = document.getElementById(modalId);

    if (!modal) {
        console.error('Modal not found');
        return;
    }

    // Get order information
    const orderDate = modal.querySelector('p:nth-of-type(1)').textContent.replace('Date: ', '');
    const orderStatus = modal.querySelector('p:nth-of-type(2)').textContent.replace('Status: ', '').trim();

    // Get customer information
    const customerName = document.getElementById('name').value;
    const customerEmail = document.getElementById('email').value;

    // Get items
    const items = [];
    const itemElements = modal.querySelectorAll('.list-group-item');
    itemElements.forEach(item => {
        const itemText = item.textContent.trim();
        const itemName = itemText.substring(0, itemText.lastIndexOf('x')).trim();
        const quantityAndPrice = itemText.substring(itemText.lastIndexOf('x')).trim();
        const quantity = quantityAndPrice.split('x')[1].trim().split(' ')[0];
        const price = item.querySelector('span').textContent;

        items.push({
            name: itemName,
            quantity: quantity,
            price: price
        });
    });

    // Get total (this is the direct total without tax)
    const total = modal.querySelector('.d-flex.justify-content-between h6:last-child').textContent;

    // Create PDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Ensure the Philippine peso symbol displays correctly
    // Use UTF-8 encoding for text
    doc.setLanguage("en-US");

    // Add restaurant info
    doc.setFontSize(20);
    doc.text("RAYMART'S DINER", 105, 20, { align: 'center' });
    doc.setFontSize(10);
    doc.text("Purok 6A, Tagbuyacan, Santiago, Agusan del Norte", 105, 30, { align: 'center' });
    doc.text("Phone: +63 ************ | Email: <EMAIL>", 105, 35, { align: 'center' });

    // Add receipt title
    doc.setFontSize(16);
    doc.text("RECEIPT", 105, 45, { align: 'center' });

    // Add order information
    doc.setFontSize(12);
    doc.text(`Receipt No: #${orderId}`, 20, 60);
    doc.text(`Date: ${orderDate}`, 20, 67);

    // Get payment method from the order details if available
    let paymentMethod = "Cash"; // Default payment method
    try {
        // Try to find payment method in the modal
        const modalContent = modal.querySelector('.modal-body');
        const paragraphs = modalContent.querySelectorAll('p');

        // Look for a paragraph that contains "Payment Method:"
        for (let i = 0; i < paragraphs.length; i++) {
            if (paragraphs[i].textContent.includes('Payment Method:')) {
                paymentMethod = paragraphs[i].textContent.replace('Payment Method:', '').trim();
                break;
            }
        }

        // If not found, try to get it from the order status
        if (paymentMethod === "Cash" && orderStatus.toLowerCase().includes('gcash')) {
            paymentMethod = "GCash";
        } else if (paymentMethod === "Cash" && orderStatus.toLowerCase().includes('credit')) {
            paymentMethod = "Credit Card";
        }
    } catch (e) {
        console.log("Could not find payment method", e);
    }

    doc.text(`Payment Method: ${paymentMethod}`, 20, 74);
    doc.text(`Customer: ${customerName}`, 20, 81);
    doc.text(`Email: ${customerEmail}`, 20, 88);
    doc.text(`Status: ${orderStatus}`, 20, 95);

    // Add items table
    const tableColumn = ["Item", "Quantity", "Price"];
    const tableRows = [];

    items.forEach(item => {
        // Extract the numeric value from the price string
        let priceText = item.price;
        let priceValue = 0;

        // Extract numeric value using regex
        const matches = priceText.match(/[\d,.]+/);
        if (matches && matches[0]) {
            // Remove commas and convert to number
            priceValue = parseFloat(matches[0].replace(/,/g, ''));
        }

        // Format with P for Philippine peso
        const formattedPrice = "P " + priceValue.toFixed(2);

        tableRows.push([item.name, item.quantity, formattedPrice]);
    });

    doc.autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 102, // Adjusted to account for the additional payment method line
        theme: 'grid',
        styles: {
            fontSize: 10
        },
        headStyles: {
            fillColor: [66, 66, 66]
        },
        columnStyles: {
            0: { cellWidth: 'auto' },
            1: { cellWidth: 20, halign: 'center' },
            2: { cellWidth: 30, halign: 'right' }
        }
    });

    // Add total
    const finalY = doc.lastAutoTable.finalY + 10;

    // Extract the numeric value from the total string
    let totalText = total;
    let totalValue = 0;

    // Extract numeric value using regex
    const totalMatches = totalText.match(/[\d,.]+/);
    if (totalMatches && totalMatches[0]) {
        // Remove commas and convert to number
        totalValue = parseFloat(totalMatches[0].replace(/,/g, ''));
    }

    // Format with P for Philippine peso
    const formattedTotal = "P " + totalValue.toFixed(2);

    doc.text(`Total: ${formattedTotal}`, 150, finalY, { align: 'right' });

    // Add thank you message
    doc.setFontSize(10);
    doc.text("Thank you for dining with us!", 105, finalY + 20, { align: 'center' });
    doc.text("Please come again.", 105, finalY + 25, { align: 'center' });

    // Save the PDF
    doc.save(`Receipt_Order_${orderId}.pdf`);
}

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Function to show add to cart notification
    function showAddToCartNotification(quantity, itemName) {
        // 1. Create a Bootstrap alert notification at the top of the page
        const alertNotification = document.createElement('div');
        alertNotification.className = 'alert alert-success fade show';
        alertNotification.style.position = 'fixed';
        alertNotification.style.top = '70px';
        alertNotification.style.left = '50%';
        alertNotification.style.transform = 'translateX(-50%)';
        alertNotification.style.zIndex = '9999';
        alertNotification.style.width = '80%';
        alertNotification.style.maxWidth = '500px';
        alertNotification.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        // Create the notification content
        alertNotification.innerHTML = `
            ${quantity}x ${itemName} added to cart successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Remove any existing alert notifications
        const existingAlerts = document.querySelectorAll('.alert.alert-success');
        existingAlerts.forEach(alert => {
            alert.remove();
        });

        // Always add to body for consistent positioning
        document.body.appendChild(alertNotification);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertNotification.parentNode) {
                alertNotification.remove();
            }
        }, 5000);

        // 2. Create the side notification
        const notification = document.createElement('div');
        notification.className = 'cart-notification';
        notification.style.display = 'flex'; // Ensure it's displayed as flex

        // Create the notification content with Tagalog message
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <div class="cart-notification-content">
                <h5>Nadagdag sa Cart!</h5>
                <p>${quantity}x ${itemName} ay nadagdag sa iyong cart</p>
            </div>
        `;

        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.cart-notification');
        existingNotifications.forEach(notif => {
            notif.remove();
        });

        // Add the notification to the page
        document.body.appendChild(notification);

        // Force a reflow to ensure the animation starts properly
        notification.offsetHeight;

        // Make sure the notification is visible
        notification.style.opacity = '1';
        notification.style.visibility = 'visible';

        // Remove the notification after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 5000);

        // Use the incrementCartCount function from cart-updater.js if available
        if (typeof incrementCartCount === 'function') {
            incrementCartCount(quantity);
            console.log("Notification: Updated cart count using cart-updater.js");
        } else {
            // Fallback if the function isn't available
            // Get the current cart count from any existing badge
            let currentCount = 0;
            const existingBadge = document.querySelector('.cart-badge');
            if (existingBadge) {
                currentCount = parseInt(existingBadge.textContent) || 0;
            }

            // Add the new quantity
            const newCount = currentCount + parseInt(quantity);

            // Update all cart icon containers
            document.querySelectorAll('.cart-icon-container').forEach(container => {
                // Find existing badge
                let badge = container.querySelector('.badge');

                // If badge doesn't exist, create a new one
                if (!badge) {
                    badge = document.createElement('span');
                    badge.className = 'badge bg-danger cart-badge';
                    container.appendChild(badge);
                }

                // Update badge text and ensure it's visible
                badge.textContent = newCount;
                badge.style.display = 'flex';
            });

            // For backward compatibility, also check for cart icons without containers
            document.querySelectorAll('.fa-shopping-cart:not(.cart-icon-container .fa-shopping-cart)').forEach(icon => {
                // Check if parent is already a container
                if (!icon.parentNode.classList.contains('cart-icon-container')) {
                    // Create container around the icon
                    const container = document.createElement('span');
                    container.className = 'cart-icon-container';

                    // Replace icon with container + icon
                    const parent = icon.parentNode;
                    parent.insertBefore(container, icon);
                    container.appendChild(icon);

                    // Create badge
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-danger cart-badge';
                    badge.textContent = newCount;
                    badge.style.display = 'flex';
                    container.appendChild(badge);
                }
            });
        }
    }
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Menu category filter
    const categoryPills = document.querySelectorAll('.category-pill');
    const menuItems = document.querySelectorAll('.menu-item');

    if (categoryPills.length > 0) {
        categoryPills.forEach(pill => {
            pill.addEventListener('click', function() {
                // Remove active class from all pills
                categoryPills.forEach(p => p.classList.remove('active'));

                // Add active class to clicked pill
                this.classList.add('active');

                const category = this.getAttribute('data-category');

                // Show all items if "All" is selected
                if (category === 'all') {
                    menuItems.forEach(item => {
                        item.style.display = 'block';
                    });
                } else {
                    // Show only items from selected category
                    menuItems.forEach(item => {
                        if (item.getAttribute('data-category') === category) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                }
            });
        });
    }

    // Quantity input controls
    const quantityInputs = document.querySelectorAll('.quantity-input');

    if (quantityInputs.length > 0) {
        quantityInputs.forEach(input => {
            const decrementBtn = input.previousElementSibling;
            const incrementBtn = input.nextElementSibling;

            decrementBtn.addEventListener('click', function() {
                if (input.value > 1) {
                    input.value = parseInt(input.value) - 1;
                    updateCartItemTotal(input);
                }
            });

            incrementBtn.addEventListener('click', function() {
                input.value = parseInt(input.value) + 1;
                updateCartItemTotal(input);
            });

            input.addEventListener('change', function() {
                if (input.value < 1) {
                    input.value = 1;
                }
                updateCartItemTotal(input);
            });
        });
    }

    // Update cart item total
    function updateCartItemTotal(input) {
        const cartItem = input.closest('.cart-item');
        if (cartItem) {
            const price = parseFloat(cartItem.querySelector('.item-price').getAttribute('data-price'));
            const quantity = parseInt(input.value);
            const totalElement = cartItem.querySelector('.item-total');

            const total = price * quantity;
            totalElement.textContent = '₱ ' + total.toFixed(2);

            // Update cart total
            updateCartTotal();
        }
    }

    // Update cart total
    function updateCartTotal() {
        const cartItems = document.querySelectorAll('.cart-item');
        let total = 0;

        cartItems.forEach(item => {
            const price = parseFloat(item.querySelector('.item-price').getAttribute('data-price'));
            const quantity = parseInt(item.querySelector('.quantity-input').value);
            total += price * quantity;
        });

        const cartTotalElement = document.getElementById('cart-total');
        if (cartTotalElement) {
            cartTotalElement.textContent = '₱ ' + total.toFixed(2);
        }
    }

    // Reservation date validation
    const reservationDateInput = document.getElementById('reservation-date');
    if (reservationDateInput) {
        // Set min date to today
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const formattedToday = `${yyyy}-${mm}-${dd}`;

        reservationDateInput.setAttribute('min', formattedToday);

        // Set max date to 30 days from now
        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 30);
        const maxYyyy = maxDate.getFullYear();
        const maxMm = String(maxDate.getMonth() + 1).padStart(2, '0');
        const maxDd = String(maxDate.getDate()).padStart(2, '0');
        const formattedMaxDate = `${maxYyyy}-${maxMm}-${maxDd}`;

        reservationDateInput.setAttribute('max', formattedMaxDate);
    }

    // Add to cart buttons
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

    if (addToCartButtons.length > 0) {
        addToCartButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Store reference to the button (this)
                const clickedButton = this;
                const itemId = clickedButton.getAttribute('data-id');
                const form = document.getElementById('add-to-cart-form-' + itemId);

                // Submit the form via AJAX
                const formData = new FormData(form);

                fetch('index.php?page=cart&action=add', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('Response received:', response);
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    if (data.success) {
                        // DIRECT APPROACH: Force update the cart badge
                        // Get the cart count from the response
                        const cartCount = data.cart_count;
                        console.log("New cart count from server:", cartCount);

                        // Use the updateCartBadges function from cart-updater.js if available
                        if (typeof updateCartBadges === 'function') {
                            updateCartBadges(cartCount);
                            console.log("Updated cart badges using cart-updater.js");
                        } else {
                            // Fallback if the function isn't available
                            // Update all cart icon containers
                            document.querySelectorAll('.cart-icon-container').forEach(container => {
                                // Find existing badge
                                let badge = container.querySelector('.badge');

                                // If badge doesn't exist, create a new one
                                if (!badge) {
                                    badge = document.createElement('span');
                                    badge.className = 'badge bg-danger cart-badge';
                                    container.appendChild(badge);
                                }

                                // Update badge text and ensure it's visible
                                badge.textContent = cartCount;
                                badge.style.display = 'flex';
                                console.log("Updated badge in container:", badge);
                            });

                            // For backward compatibility, also check for cart icons without containers
                            document.querySelectorAll('.fa-shopping-cart:not(.cart-icon-container .fa-shopping-cart)').forEach(icon => {
                                // Check if parent is already a container
                                if (!icon.parentNode.classList.contains('cart-icon-container')) {
                                    // Create container around the icon
                                    const container = document.createElement('span');
                                    container.className = 'cart-icon-container';

                                    // Replace icon with container + icon
                                    const parent = icon.parentNode;
                                    parent.insertBefore(container, icon);
                                    container.appendChild(icon);

                                    // Create badge
                                    const badge = document.createElement('span');
                                    badge.className = 'badge bg-danger cart-badge';
                                    badge.textContent = cartCount;
                                    badge.style.display = 'flex';
                                    container.appendChild(badge);
                                    console.log("Created new badge in new container:", badge);
                                }
                            });
                        }
                        // Add visual feedback to the button
                        const originalButtonText = clickedButton.innerHTML;
                        clickedButton.innerHTML = '<i class="fas fa-check"></i> Added!';
                        clickedButton.classList.add('btn-success');
                        clickedButton.classList.remove('btn-primary');

                        // Create flying item animation
                        const buttonRect = clickedButton.getBoundingClientRect();
                        const cartIcon = document.querySelector('.fa-shopping-cart');

                        if (cartIcon) {
                            const cartRect = cartIcon.getBoundingClientRect();

                            // Try to find the menu item image
                            let menuItemImage = null;
                            const menuItemCard = clickedButton.closest('.menu-item, .card');
                            if (menuItemCard) {
                                menuItemImage = menuItemCard.querySelector('img');
                            }

                            // Create the flying element
                            const flyingItem = document.createElement('div');
                            flyingItem.className = 'cart-item-fly';

                            if (menuItemImage) {
                                // Use the actual food image if available
                                flyingItem.style.backgroundImage = `url(${menuItemImage.src})`;
                                flyingItem.style.backgroundSize = 'cover';
                                flyingItem.style.backgroundPosition = 'center';
                            } else {
                                // Fallback to icon
                                flyingItem.innerHTML = '<i class="fas fa-utensils"></i>';
                            }

                            // Set initial position (center of the button)
                            flyingItem.style.top = (buttonRect.top + buttonRect.height/2) + 'px';
                            flyingItem.style.left = (buttonRect.left + buttonRect.width/2) + 'px';

                            // Add to body
                            document.body.appendChild(flyingItem);

                            // Force reflow
                            flyingItem.offsetHeight;

                            // Add flying class
                            flyingItem.classList.add('flying');

                            // Set final position (cart icon)
                            flyingItem.style.top = (cartRect.top + cartRect.height/2) + 'px';
                            flyingItem.style.left = (cartRect.left + cartRect.width/2) + 'px';

                            // Remove the element after animation completes
                            setTimeout(() => {
                                flyingItem.remove();

                                // Trigger cart icon animation after flying animation completes
                                cartIcon.classList.add('cart-icon-animate');
                                setTimeout(() => {
                                    cartIcon.classList.remove('cart-icon-animate');
                                }, 1000);

                                // Update cart badge count immediately
                                const cartCount = data.cart_count;
                                console.log("Flying animation: New cart count from server:", cartCount);

                                // Use the updateCartBadges function from cart-updater.js if available
                                if (typeof updateCartBadges === 'function') {
                                    updateCartBadges(cartCount);
                                    console.log("Flying animation: Updated cart badges using cart-updater.js");
                                } else {
                                    // Fallback if the function isn't available
                                    // Update all cart icon containers
                                    document.querySelectorAll('.cart-icon-container').forEach(container => {
                                        // Find existing badge
                                        let badge = container.querySelector('.badge');

                                        // If badge doesn't exist, create a new one
                                        if (!badge) {
                                            badge = document.createElement('span');
                                            badge.className = 'badge bg-danger cart-badge';
                                            container.appendChild(badge);
                                        }

                                        // Update badge text and ensure it's visible
                                        badge.textContent = cartCount;
                                        badge.style.display = 'flex';
                                    });

                                    // For backward compatibility, also check for cart icons without containers
                                    document.querySelectorAll('.fa-shopping-cart:not(.cart-icon-container .fa-shopping-cart)').forEach(icon => {
                                        // Check if parent is already a container
                                        if (!icon.parentNode.classList.contains('cart-icon-container')) {
                                            // Create container around the icon
                                            const container = document.createElement('span');
                                            container.className = 'cart-icon-container';

                                            // Replace icon with container + icon
                                            const parent = icon.parentNode;
                                            parent.insertBefore(container, icon);
                                            container.appendChild(icon);

                                            // Create badge
                                            const badge = document.createElement('span');
                                            badge.className = 'badge bg-danger cart-badge';
                                            badge.textContent = cartCount;
                                            badge.style.display = 'flex';
                                            container.appendChild(badge);
                                        }
                                    });
                                }

                                // Show notification after flying animation
                                showAddToCartNotification(quantity, itemName);
                            }, 800);
                        }

                        // Restore button after 1.5 seconds
                        setTimeout(() => {
                            clickedButton.innerHTML = originalButtonText;
                            clickedButton.classList.remove('btn-success');
                            clickedButton.classList.add('btn-primary');
                        }, 1500);
                        // Get the item name from the form's parent element
                        let itemName = 'Item';

                        // Handle different page layouts
                        // First try to find the card-title within the closest card
                        const itemCard = form.closest('.card');
                        if (itemCard && itemCard.querySelector('.card-title')) {
                            // Menu page layout
                            itemName = itemCard.querySelector('.card-title').textContent.trim();
                        } else {
                            // Try to find the menu-item-title within the closest menu-item
                            const menuItem = form.closest('.menu-item');
                            if (menuItem) {
                                const titleElement = menuItem.querySelector('.menu-item-title');
                                if (titleElement) {
                                    itemName = titleElement.textContent.trim();
                                } else {
                                    // If we're in a menu-item but can't find the title, look for any h5 element
                                    const h5Element = menuItem.querySelector('h5');
                                    if (h5Element) {
                                        itemName = h5Element.textContent.trim();
                                    }
                                }
                            } else {
                                // If all else fails, try to get the item name from the form's hidden input
                                const itemId = form.querySelector('input[name="item_id"]').value;
                                // Use the item ID as a fallback
                                itemName = `Item #${itemId}`;
                            }
                        }

                        console.log("Item name found:", itemName);
                        const quantity = form.querySelector('input[name="quantity"]').value;

                        // If no cart icon animation was shown (direct notification)
                        if (!cartIcon) {
                            // Find cart icon if not already found
                            const navCartIcon = document.querySelector('.fa-shopping-cart');
                            showAddToCartNotification(quantity, itemName);

                            // Animate the cart icon if found
                            if (navCartIcon) {
                                navCartIcon.classList.add('cart-icon-animate');
                                setTimeout(() => {
                                    navCartIcon.classList.remove('cart-icon-animate');
                                }, 1000);
                            }
                        }

                        // Update cart count in navbar
                        const navCartIcon = document.querySelector('.fa-shopping-cart');
                        const cartBadge = navCartIcon ? navCartIcon.nextElementSibling : null;

                        // Only animate if not already animated by the flying animation
                        if (!cartIcon && navCartIcon) {
                            // Animate the cart icon
                            navCartIcon.classList.add('cart-icon-animate');
                            setTimeout(() => {
                                navCartIcon.classList.remove('cart-icon-animate');
                            }, 1000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
    }
});
