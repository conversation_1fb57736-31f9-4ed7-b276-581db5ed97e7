<?php
session_start();
include '../../config/config.php';
include '../../includes/functions.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get the chart type
$chart_type = isset($_GET['chart_type']) ? sanitize($_GET['chart_type']) : '';

// Initialize response array
$response = [];

switch ($chart_type) {
    case 'payment_method':
        // Query to get payment method distribution
        $query = "SELECT
                    payment_method,
                    COUNT(*) as count
                  FROM payment_transactions
                  GROUP BY payment_method";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->get_result();

        $payment_methods = [];
        $payment_counts = [];
        $payment_colors = [
            'cash' => 'rgba(75, 192, 192, 0.7)',
            'credit_card' => 'rgba(54, 162, 235, 0.7)',
            'debit_card' => 'rgba(153, 102, 255, 0.7)',
            'gcash' => 'rgba(255, 159, 64, 0.7)',
            'paymaya' => 'rgba(255, 99, 132, 0.7)'
        ];
        $colors = [];

        while ($row = $result->fetch_assoc()) {
            $method = ucwords(str_replace('_', ' ', $row['payment_method']));
            $payment_methods[] = $method;
            $payment_counts[] = (int)$row['count'];
            $colors[] = $payment_colors[$row['payment_method']] ?? 'rgba(128, 128, 128, 0.7)';
        }

        // If no data, provide default values
        if (empty($payment_methods)) {
            $payment_methods = ['Cash', 'Credit Card', 'Debit Card', 'GCash', 'PayMaya'];
            $payment_counts = [0, 0, 0, 0, 0];
            $colors = array_values($payment_colors);
        }

        $response = [
            'labels' => $payment_methods,
            'data' => $payment_counts,
            'backgroundColor' => $colors
        ];
        break;

    case 'transaction_status':
        // Query to get transaction status distribution
        $query = "SELECT
                    transaction_status,
                    COUNT(*) as count
                  FROM payment_transactions
                  GROUP BY transaction_status";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->get_result();

        $status_labels = [];
        $status_counts = [];
        $status_colors = [
            'pending' => 'rgba(255, 205, 86, 0.7)',
            'completed' => 'rgba(75, 192, 192, 0.7)',
            'failed' => 'rgba(255, 99, 132, 0.7)'
        ];
        $status_bg_colors = [];

        while ($row = $result->fetch_assoc()) {
            $status = ucwords(str_replace('_', ' ', $row['transaction_status']));
            $status_labels[] = $status;
            $status_counts[] = (int)$row['count'];
            $status_bg_colors[] = $status_colors[$row['transaction_status']] ?? 'rgba(128, 128, 128, 0.7)';
        }

        // If no data, provide default values
        if (empty($status_labels)) {
            $status_labels = ['Pending', 'Completed', 'Failed', 'Refunded', 'Partially Refunded'];
            $status_counts = [0, 0, 0, 0, 0];
            $status_bg_colors = array_values($status_colors);
        }

        $response = [
            'labels' => $status_labels,
            'data' => $status_counts,
            'backgroundColor' => $status_bg_colors
        ];
        break;

    case 'monthly_volume':
        // Get transaction data for the current month
        $current_month = date('Y-m');
        $current_month_name = date('F Y');

        // Query to get transaction volume for the current month
        $query = "SELECT SUM(amount) as total_amount
                  FROM payment_transactions
                  WHERE DATE_FORMAT(transaction_date, '%Y-%m') = ?
                  AND transaction_status != 'failed'";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $current_month);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        $current_month_volume = $row['total_amount'] ? (float)$row['total_amount'] : 0;

        $response = [
            'labels' => [$current_month_name],
            'data' => [$current_month_volume]
        ];
        break;

    default:
        $response = ['error' => 'Invalid chart type'];
        break;
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit();
?>
